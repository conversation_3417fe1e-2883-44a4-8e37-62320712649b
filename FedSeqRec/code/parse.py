import argparse
import yaml

def parse_args():
    parser = argparse.ArgumentParser()

    # 首先加载config文件
    config = yaml.safe_load (open ('./config/basic.yaml', 'r'))

    parser.add_argument('--model',type = str,default ='BSARec' ,choices = ['SASRec','FMLP', 'BSARec'],help='选择推荐模型')

    # 调整为与参考项目SAS.torch一致的参数配置
    parser.add_argument('--lr', type=float, default=0.001, help='Learning rate (参考SAS.torch: 0.001)')
    parser.add_argument('--batch_size', type=int, default=128, help='Batch size (参考SAS.torch: 128)')
    parser.add_argument ('--neg_num', type = int, default = 99, help = '负样本采样数量')
    parser.add_argument('--l2_reg', type=float, default=0, help='L2 regularization coefficient')
    parser.add_argument('--l2_emb', type=float, default=0.0, help='L2 regularization for embeddings (参考SAS.torch: 0.0)')
    parser.add_argument('--hidden_size', type=int, default=50, help='The dimension for item embeddings and all hidden layers.')
    parser.add_argument('--dropout', type=float, default=0.2, help='dropout rate (参考SAS.torch: 0.2)')
    parser.add_argument('--epochs', type=int, default=1000, help='训练轮数 ')
    parser.add_argument('--early_stop', type=int, default=30, help='Patience for early stop')
    parser.add_argument('--datapath', type=str, default='../../data/', help='Data path')
    parser.add_argument('--dataset', type=str, default='ml-100k', help='Dataset name')
    parser.add_argument('--train_data', type=str, default='ml-100k.txt', help='train dataset')
    parser.add_argument ('--log_path', type = str, default = '../log', help = 'logger path')
    parser.add_argument('--num_layers', type=int, default=2, help='Transformer层数 (参考SAS.torch num_blocks: 2)')
    parser.add_argument('--num_heads', type=int, default=1, help='注意力头数量 (参考SAS.torch: 1)')
    parser.add_argument('--inner_size', type=int, default=256, help='前馈网络内部大小')
    parser.add_argument('--max_seq_len', type=int, default=200, help='最大序列长度 (参考SAS.torch maxlen: 200)')

    # 参数上传模式
    parser.add_argument('--upload_mode', type=str, default='full',
                        choices=['partial', 'full'],
                        help='参数上传模式: partial(部分上传-仅embedding,隐私参数本地训练), full(全部上传到服务器)')

    # 测试集评估控制参数
    parser.add_argument('--skip_test_eval', action='store_true', default=False,
                        help='跳过测试集评估以节省训练时间，只进行验证集评估')

    # 评估频率控制参数
    parser.add_argument('--eval_freq', type=int, default=1,
                        help='每隔多少个epoch进行一次评估，默认为20')

    # 网络速度配置
    parser.add_argument('--download_speed', type=float, default=5.0, help='客户端下载速度 (MB/s)')
    parser.add_argument('--upload_speed', type=float, default=5.0, help='客户端上传速度 (MB/s)')

#  ml-1m原论文：c=9  alpha=0.3 | 基准论文: c=1 alpha=0.7
    parser.add_argument('--c', type=int, default=9, help='c for BSARec ,用来在傅里叶变换后的频域中，区分什么是低频信号，什么是高频信号，低于C为低频信号，高于C为高频信息')
    parser.add_argument('--alpha', type=float, default=0.3, help='alpha for BSARec, 用来平衡模型对“从数据中学到的复杂模式（自注意力）”和“预设的通用规律（傅里叶归纳偏置）”的依赖程度')

    args = parser.parse_args ()
    config.update (vars (args))  # 使用这个字典更新之前从 YAML 文件加载的配置字典 config

    return config

config = parse_args()