import os
import datetime
from Fed import Clients, Server
from parse import config
from untils import Logger

if __name__ == '__main__':

    # 创建日志记录器
    logger = Logger(config)
    logger.info("开始训练，配置参数如下：")
    for key, value in config.items():
        logger.info(f"{key}: {value}")

    # 打印数据集路径信息
    train_data_path = config['datapath'] + config['dataset'] + '/' + config['train_data']
    logger.info(f"训练数据: {train_data_path}")


    logger.info(f"最大序列长度: {config.get('max_seq_len', 200)}")
    logger.info(f"批次大小: {config['batch_size']}")


    # 构建客户端
    clients = Clients(config, logger)
    logger.info(f"用户数量: {clients.usernum}")
    logger.info(f"物品数量: {clients.itemnum}")

    # 构建服务器
    server = Server(config, clients, logger)

    # 开始训练
    server.train()

