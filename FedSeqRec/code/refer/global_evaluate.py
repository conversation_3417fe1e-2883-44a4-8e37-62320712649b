def evaluate ( self, data_loader ):
    """
    通用评估方法，用于在测试集或验证集上评估模型性能

    参数:
    - data_loader: 数据加载器 (测试集或验证集)

    返回:
    - ndcg10: NDCG@10指标
    - hr10: HR@10指标
    - auc: AUC指标
    - eval_time: 评估用时
    """
    # 记录评估开始时间
    start_time = time.time ()

    # 切换模型到评估模式
    self.model.eval ()

    # 初始化评估指标列表
    ndcg10_list, hr10_list, auc_list = [], [], []

    # 在评估过程中不计算梯度，节省内存和计算时间
    with torch.no_grad ():
        for input_seq, input_len, train_vec, target_vec in data_loader:
            # 将数据移动到指定设备
            input_seq = input_seq.to (self.device)
            input_len = input_len.to (self.device)

            # 处理序列长度限制
            max_seq_length = self.model.max_seq_length
            if input_seq.size (1) > max_seq_length:
                # 保留序列后部分（最近交互）
                input_seq = input_seq [:, -max_seq_length:]
                input_len = torch.clamp (input_len, max = max_seq_length)

            # 获取模型预测
            pro = self.model (input_seq, input_len)
            recon_batch = []

            # 提取每个序列最后一个时间步的预测结果
            for i in range (input_seq.shape [0]):
                # 对于前填充序列，计算最后一个有效位置
                last_idx = min (input_seq.shape [1] - 1, input_len [i].item () - 1)
                scores = pro [i, last_idx, :].unsqueeze (0)
                recon_batch.append (scores)

            # 合并所有预测结果
            recon_batch = torch.cat (recon_batch)
            recon_batch = recon_batch.detach ().cpu ().numpy ()

            # 准备评估用的真实数据
            train_vec = train_vec.numpy ()
            target_vec = target_vec.numpy ()

            # 过滤掉用户已经交互过的物品
            # 将已交互物品的分数设为负无穷，确保不会被推荐
            recon_batch [train_vec.nonzero ()] = -np.inf

            # 计算各种评估指标
            n_10 = NDCG_binary_at_k_batch (recon_batch [:, 1:], target_vec [:, 1:], 10)
            auc_b = AUC_at_k_batch (train_vec [:, 1:], recon_batch [:, 1:], target_vec [:, 1:])
            hr_10 = HR_at_k_batch (recon_batch [:, 1:], target_vec [:, 1:], 10)

            # 收集评估结果
            auc_list.append (auc_b)
            ndcg10_list.append (n_10)
            hr10_list.append (hr_10)

    # 计算各指标的平均值
    auc = np.mean (np.concatenate (auc_list))
    ndcg10 = np.mean (np.concatenate (ndcg10_list))
    hr10 = np.mean (hr10_list)

    # 计算总评估用时
    eval_time = time.time () - start_time

    return ndcg10, hr10, auc, eval_time