{'early_stop_enabled': True, 'model': 'SASRec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'Toys_and_Games', 'train_data': 'Toys_and_Games.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False, 'c': 9, 'alpha': 0.3}
[Aug-05-2025_09-27-51] - 开始训练，配置参数如下：
[Aug-05-2025_09-27-51] - early_stop_enabled: True
[Aug-05-2025_09-27-51] - model: SASRec
[Aug-05-2025_09-27-51] - lr: 0.001
[Aug-05-2025_09-27-51] - batch_size: 128
[Aug-05-2025_09-27-51] - neg_num: 99
[Aug-05-2025_09-27-51] - l2_reg: 0
[Aug-05-2025_09-27-51] - l2_emb: 0.0
[Aug-05-2025_09-27-51] - hidden_size: 50
[Aug-05-2025_09-27-51] - dropout: 0.2
[Aug-05-2025_09-27-51] - epochs: 1000000
[Aug-05-2025_09-27-51] - early_stop: 50
[Aug-05-2025_09-27-51] - datapath: ../../data/
[Aug-05-2025_09-27-51] - dataset: Toys_and_Games
[Aug-05-2025_09-27-51] - train_data: Toys_and_Games.txt
[Aug-05-2025_09-27-51] - log_path: ../log
[Aug-05-2025_09-27-51] - num_layers: 2
[Aug-05-2025_09-27-51] - num_heads: 1
[Aug-05-2025_09-27-51] - inner_size: 256
[Aug-05-2025_09-27-51] - max_seq_len: 200
[Aug-05-2025_09-27-51] - upload_mode: full
[Aug-05-2025_09-27-51] - skip_test_eval: True
[Aug-05-2025_09-27-51] - eval_freq: 1
[Aug-05-2025_09-27-51] - use_dynamic_sampling: False
[Aug-05-2025_09-27-51] - norm_first: False
[Aug-05-2025_09-27-51] - c: 9
[Aug-05-2025_09-27-51] - alpha: 0.3
[Aug-05-2025_09-27-51] - 训练数据: ../../data/Toys_and_Games/Toys_and_Games.txt
[Aug-05-2025_09-27-51] - 最大序列长度: 200
[Aug-05-2025_09-27-51] - 批次大小: 128
[Aug-05-2025_09-27-53] - 参数上传模式: full
[Aug-05-2025_09-27-53] - 隐私参数（本地更新）: []
[Aug-05-2025_09-27-53] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'trm_encoder.layer.0.multi_head_attention.query.weight', 'trm_encoder.layer.0.multi_head_attention.query.bias', 'trm_encoder.layer.0.multi_head_attention.key.weight', 'trm_encoder.layer.0.multi_head_attention.key.bias', 'trm_encoder.layer.0.multi_head_attention.value.weight', 'trm_encoder.layer.0.multi_head_attention.value.bias', 'trm_encoder.layer.0.multi_head_attention.dense.weight', 'trm_encoder.layer.0.multi_head_attention.dense.bias', 'trm_encoder.layer.0.multi_head_attention.LayerNorm.weight', 'trm_encoder.layer.0.multi_head_attention.LayerNorm.bias', 'trm_encoder.layer.0.feed_forward.dense_1.weight', 'trm_encoder.layer.0.feed_forward.dense_1.bias', 'trm_encoder.layer.0.feed_forward.dense_2.weight', 'trm_encoder.layer.0.feed_forward.dense_2.bias', 'trm_encoder.layer.0.feed_forward.LayerNorm.weight', 'trm_encoder.layer.0.feed_forward.LayerNorm.bias', 'trm_encoder.layer.1.multi_head_attention.query.weight', 'trm_encoder.layer.1.multi_head_attention.query.bias', 'trm_encoder.layer.1.multi_head_attention.key.weight', 'trm_encoder.layer.1.multi_head_attention.key.bias', 'trm_encoder.layer.1.multi_head_attention.value.weight', 'trm_encoder.layer.1.multi_head_attention.value.bias', 'trm_encoder.layer.1.multi_head_attention.dense.weight', 'trm_encoder.layer.1.multi_head_attention.dense.bias', 'trm_encoder.layer.1.multi_head_attention.LayerNorm.weight', 'trm_encoder.layer.1.multi_head_attention.LayerNorm.bias', 'trm_encoder.layer.1.feed_forward.dense_1.weight', 'trm_encoder.layer.1.feed_forward.dense_1.bias', 'trm_encoder.layer.1.feed_forward.dense_2.weight', 'trm_encoder.layer.1.feed_forward.dense_2.bias', 'trm_encoder.layer.1.feed_forward.LayerNorm.weight', 'trm_encoder.layer.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-05-2025_09-27-53] - 动态负采样: False
[Aug-05-2025_09-27-53] - 用户数量: 19412
[Aug-05-2025_09-27-53] - 物品数量: 11924
[Aug-05-2025_09-33-25] - epoch:1, time: 284.387218(s), valid (NDCG@10: 0.1217, HR@10: 0.2369), test: SKIPPED, all_time: 284.387218(s)
[Aug-05-2025_09-33-25] - 新的最佳性能: valid NDCG@10=0.1217, valid HR@10=0.2369
[Aug-05-2025_09-39-11] - epoch:2, time: 292.836729(s), valid (NDCG@10: 0.1318, HR@10: 0.2606), test: SKIPPED, all_time: 577.223948(s)
[Aug-05-2025_09-39-11] - 新的最佳性能: valid NDCG@10=0.1318, valid HR@10=0.2606
