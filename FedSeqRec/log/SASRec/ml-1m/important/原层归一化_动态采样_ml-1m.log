{'early_stop_enabled': True, 'lr': 0.001, 'batch_size': 128, 'l2_reg': 0, 'l2_emb': 0.0, 'embed_dim': 50, 'hidden_size': 32, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'ml-1m', 'train_data': 'ml-1m.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': True, 'norm_first': False}
[Jul-24-2025_10-19-23] - 开始训练，配置参数如下：
[Jul-24-2025_10-19-23] - early_stop_enabled: True
[Jul-24-2025_10-19-23] - lr: 0.001
[Jul-24-2025_10-19-23] - batch_size: 128
[Jul-24-2025_10-19-23] - l2_reg: 0
[Jul-24-2025_10-19-23] - l2_emb: 0.0
[Jul-24-2025_10-19-23] - embed_dim: 50
[Jul-24-2025_10-19-23] - hidden_size: 32
[Jul-24-2025_10-19-23] - dropout: 0.2
[Jul-24-2025_10-19-23] - epochs: 1000000
[Jul-24-2025_10-19-23] - early_stop: 50
[Jul-24-2025_10-19-23] - datapath: ../../data/
[Jul-24-2025_10-19-23] - dataset: ml-1m
[Jul-24-2025_10-19-23] - train_data: ml-1m.txt
[Jul-24-2025_10-19-23] - log_path: ../log
[Jul-24-2025_10-19-23] - num_layers: 2
[Jul-24-2025_10-19-23] - num_heads: 1
[Jul-24-2025_10-19-23] - inner_size: 256
[Jul-24-2025_10-19-23] - max_seq_len: 200
[Jul-24-2025_10-19-23] - upload_mode: full
[Jul-24-2025_10-19-23] - skip_test_eval: True
[Jul-24-2025_10-19-23] - eval_freq: 1
[Jul-24-2025_10-19-23] - use_dynamic_sampling: True
[Jul-24-2025_10-19-23] - norm_first: False
[Jul-24-2025_10-19-23] - 训练数据: ../../data/ml-1m/ml-1m.txt
[Jul-24-2025_10-19-23] - 最大序列长度: 200
[Jul-24-2025_10-19-23] - 批次大小: 128
[Jul-24-2025_10-19-27] - 参数上传模式: full
[Jul-24-2025_10-19-27] - 隐私参数（本地更新）: []
[Jul-24-2025_10-19-27] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'trm_encoder.layer.0.multi_head_attention.query.weight', 'trm_encoder.layer.0.multi_head_attention.query.bias', 'trm_encoder.layer.0.multi_head_attention.key.weight', 'trm_encoder.layer.0.multi_head_attention.key.bias', 'trm_encoder.layer.0.multi_head_attention.value.weight', 'trm_encoder.layer.0.multi_head_attention.value.bias', 'trm_encoder.layer.0.multi_head_attention.dense.weight', 'trm_encoder.layer.0.multi_head_attention.dense.bias', 'trm_encoder.layer.0.multi_head_attention.LayerNorm.weight', 'trm_encoder.layer.0.multi_head_attention.LayerNorm.bias', 'trm_encoder.layer.0.feed_forward.dense_1.weight', 'trm_encoder.layer.0.feed_forward.dense_1.bias', 'trm_encoder.layer.0.feed_forward.dense_2.weight', 'trm_encoder.layer.0.feed_forward.dense_2.bias', 'trm_encoder.layer.0.feed_forward.LayerNorm.weight', 'trm_encoder.layer.0.feed_forward.LayerNorm.bias', 'trm_encoder.layer.1.multi_head_attention.query.weight', 'trm_encoder.layer.1.multi_head_attention.query.bias', 'trm_encoder.layer.1.multi_head_attention.key.weight', 'trm_encoder.layer.1.multi_head_attention.key.bias', 'trm_encoder.layer.1.multi_head_attention.value.weight', 'trm_encoder.layer.1.multi_head_attention.value.bias', 'trm_encoder.layer.1.multi_head_attention.dense.weight', 'trm_encoder.layer.1.multi_head_attention.dense.bias', 'trm_encoder.layer.1.multi_head_attention.LayerNorm.weight', 'trm_encoder.layer.1.multi_head_attention.LayerNorm.bias', 'trm_encoder.layer.1.feed_forward.dense_1.weight', 'trm_encoder.layer.1.feed_forward.dense_1.bias', 'trm_encoder.layer.1.feed_forward.dense_2.weight', 'trm_encoder.layer.1.feed_forward.dense_2.bias', 'trm_encoder.layer.1.feed_forward.LayerNorm.weight', 'trm_encoder.layer.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Jul-24-2025_10-19-27] - 动态负采样: True
[Jul-24-2025_10-19-27] - 用户数量: 6040
[Jul-24-2025_10-19-27] - 物品数量: 3416
[Jul-24-2025_10-28-03] - epoch:1, time: 485.903971(s), valid (NDCG@10: 0.2415, HR@10: 0.4379), test: SKIPPED, all_time: 485.903971(s)
[Jul-24-2025_10-28-03] - 新的最佳性能: valid NDCG@10=0.2415, valid HR@10=0.4379
[Jul-24-2025_10-37-02] - epoch:2, time: 508.238878(s), valid (NDCG@10: 0.2430, HR@10: 0.4394), test: SKIPPED, all_time: 994.142849(s)
[Jul-24-2025_10-37-02] - 新的最佳性能: valid NDCG@10=0.2430, valid HR@10=0.4394
[Jul-24-2025_10-45-58] - epoch:3, time: 505.936857(s), valid (NDCG@10: 0.2448, HR@10: 0.4452), test: SKIPPED, all_time: 1500.079705(s)
[Jul-24-2025_10-45-58] - 新的最佳性能: valid NDCG@10=0.2448, valid HR@10=0.4452
[Jul-24-2025_10-54-55] - epoch:4, time: 506.441917(s), valid (NDCG@10: 0.2750, HR@10: 0.5003), test: SKIPPED, all_time: 2006.521622(s)
[Jul-24-2025_10-54-55] - 新的最佳性能: valid NDCG@10=0.2750, valid HR@10=0.5003
[Jul-24-2025_11-03-53] - epoch:5, time: 506.505203(s), valid (NDCG@10: 0.3305, HR@10: 0.5775), test: SKIPPED, all_time: 2513.026825(s)
[Jul-24-2025_11-03-53] - 新的最佳性能: valid NDCG@10=0.3305, valid HR@10=0.5775
[Jul-24-2025_11-12-50] - epoch:6, time: 506.577213(s), valid (NDCG@10: 0.3534, HR@10: 0.6121), test: SKIPPED, all_time: 3019.604038(s)
[Jul-24-2025_11-12-50] - 新的最佳性能: valid NDCG@10=0.3534, valid HR@10=0.6121
[Jul-24-2025_11-21-49] - epoch:7, time: 507.930304(s), valid (NDCG@10: 0.3766, HR@10: 0.6326), test: SKIPPED, all_time: 3527.534342(s)
[Jul-24-2025_11-21-49] - 新的最佳性能: valid NDCG@10=0.3766, valid HR@10=0.6326
[Jul-24-2025_11-30-48] - epoch:8, time: 507.656856(s), valid (NDCG@10: 0.3983, HR@10: 0.6631), test: SKIPPED, all_time: 4035.191197(s)
[Jul-24-2025_11-30-48] - 新的最佳性能: valid NDCG@10=0.3983, valid HR@10=0.6631
[Jul-24-2025_11-39-47] - epoch:9, time: 507.743263(s), valid (NDCG@10: 0.4146, HR@10: 0.6788), test: SKIPPED, all_time: 4542.934460(s)
[Jul-24-2025_11-39-47] - 新的最佳性能: valid NDCG@10=0.4146, valid HR@10=0.6788
[Jul-24-2025_11-48-44] - epoch:10, time: 506.740923(s), valid (NDCG@10: 0.4263, HR@10: 0.6937), test: SKIPPED, all_time: 5049.675383(s)
[Jul-24-2025_11-48-44] - 新的最佳性能: valid NDCG@10=0.4263, valid HR@10=0.6937
[Jul-24-2025_11-57-42] - epoch:11, time: 507.450845(s), valid (NDCG@10: 0.4431, HR@10: 0.7096), test: SKIPPED, all_time: 5557.126228(s)
[Jul-24-2025_11-57-42] - 新的最佳性能: valid NDCG@10=0.4431, valid HR@10=0.7096
[Jul-24-2025_12-06-40] - epoch:12, time: 507.312562(s), valid (NDCG@10: 0.4549, HR@10: 0.7245), test: SKIPPED, all_time: 6064.438790(s)
[Jul-24-2025_12-06-40] - 新的最佳性能: valid NDCG@10=0.4549, valid HR@10=0.7245
[Jul-24-2025_12-15-38] - epoch:13, time: 506.883803(s), valid (NDCG@10: 0.4588, HR@10: 0.7268), test: SKIPPED, all_time: 6571.322592(s)
[Jul-24-2025_12-15-38] - 新的最佳性能: valid NDCG@10=0.4588, valid HR@10=0.7268
[Jul-24-2025_12-24-38] - epoch:14, time: 509.032435(s), valid (NDCG@10: 0.4668, HR@10: 0.7376), test: SKIPPED, all_time: 7080.355028(s)
[Jul-24-2025_12-24-38] - 新的最佳性能: valid NDCG@10=0.4668, valid HR@10=0.7376
[Jul-24-2025_12-33-39] - epoch:15, time: 509.757984(s), valid (NDCG@10: 0.4722, HR@10: 0.7440), test: SKIPPED, all_time: 7590.113012(s)
[Jul-24-2025_12-33-39] - 新的最佳性能: valid NDCG@10=0.4722, valid HR@10=0.7440
[Jul-24-2025_12-42-39] - epoch:16, time: 509.192329(s), valid (NDCG@10: 0.4821, HR@10: 0.7518), test: SKIPPED, all_time: 8099.305341(s)
[Jul-24-2025_12-42-39] - 新的最佳性能: valid NDCG@10=0.4821, valid HR@10=0.7518
[Jul-24-2025_12-51-38] - epoch:17, time: 507.992155(s), valid (NDCG@10: 0.4874, HR@10: 0.7526), test: SKIPPED, all_time: 8607.297495(s)
[Jul-24-2025_12-51-38] - 新的最佳性能: valid NDCG@10=0.4874, valid HR@10=0.7526
[Jul-24-2025_13-00-36] - epoch:18, time: 506.973526(s), valid (NDCG@10: 0.4921, HR@10: 0.7551), test: SKIPPED, all_time: 9114.271022(s)
[Jul-24-2025_13-00-36] - 新的最佳性能: valid NDCG@10=0.4921, valid HR@10=0.7551
[Jul-24-2025_13-09-33] - epoch:19, time: 506.294381(s), valid (NDCG@10: 0.4987, HR@10: 0.7659), test: SKIPPED, all_time: 9620.565403(s)
[Jul-24-2025_13-09-33] - 新的最佳性能: valid NDCG@10=0.4987, valid HR@10=0.7659
[Jul-24-2025_13-18-30] - epoch:20, time: 507.032257(s), valid (NDCG@10: 0.5037, HR@10: 0.7647), test: SKIPPED, all_time: 10127.597660(s)
[Jul-24-2025_13-27-27] - epoch:21, time: 506.175589(s), valid (NDCG@10: 0.5031, HR@10: 0.7659), test: SKIPPED, all_time: 10633.773249(s)
[Jul-24-2025_13-36-25] - epoch:22, time: 507.144919(s), valid (NDCG@10: 0.5106, HR@10: 0.7694), test: SKIPPED, all_time: 11140.918168(s)
[Jul-24-2025_13-36-25] - 新的最佳性能: valid NDCG@10=0.5106, valid HR@10=0.7694
[Jul-24-2025_13-45-21] - epoch:23, time: 504.807507(s), valid (NDCG@10: 0.5121, HR@10: 0.7685), test: SKIPPED, all_time: 11645.725675(s)
[Jul-24-2025_13-54-19] - epoch:24, time: 506.423097(s), valid (NDCG@10: 0.5145, HR@10: 0.7724), test: SKIPPED, all_time: 12152.148771(s)
[Jul-24-2025_13-54-19] - 新的最佳性能: valid NDCG@10=0.5145, valid HR@10=0.7724
[Jul-24-2025_14-03-15] - epoch:25, time: 505.824739(s), valid (NDCG@10: 0.5139, HR@10: 0.7747), test: SKIPPED, all_time: 12657.973511(s)
[Jul-24-2025_14-03-15] - 新的最佳性能: valid NDCG@10=0.5145, valid HR@10=0.7747
[Jul-24-2025_14-12-12] - epoch:26, time: 505.916730(s), valid (NDCG@10: 0.5136, HR@10: 0.7755), test: SKIPPED, all_time: 13163.890240(s)
[Jul-24-2025_14-12-12] - 新的最佳性能: valid NDCG@10=0.5145, valid HR@10=0.7755
[Jul-24-2025_14-21-11] - epoch:27, time: 508.650831(s), valid (NDCG@10: 0.5140, HR@10: 0.7770), test: SKIPPED, all_time: 13672.541071(s)
[Jul-24-2025_14-21-11] - 新的最佳性能: valid NDCG@10=0.5145, valid HR@10=0.7770
[Jul-24-2025_14-30-07] - epoch:28, time: 505.364806(s), valid (NDCG@10: 0.5188, HR@10: 0.7750), test: SKIPPED, all_time: 14177.905877(s)
[Jul-24-2025_14-39-05] - epoch:29, time: 507.106823(s), valid (NDCG@10: 0.5187, HR@10: 0.7773), test: SKIPPED, all_time: 14685.012701(s)
[Jul-24-2025_14-39-05] - 新的最佳性能: valid NDCG@10=0.5188, valid HR@10=0.7773
[Jul-24-2025_14-48-02] - epoch:30, time: 505.689503(s), valid (NDCG@10: 0.5195, HR@10: 0.7785), test: SKIPPED, all_time: 15190.702203(s)
[Jul-24-2025_14-48-02] - 新的最佳性能: valid NDCG@10=0.5195, valid HR@10=0.7785
[Jul-24-2025_14-56-59] - epoch:31, time: 507.026895(s), valid (NDCG@10: 0.5244, HR@10: 0.7808), test: SKIPPED, all_time: 15697.729098(s)
[Jul-24-2025_14-56-59] - 新的最佳性能: valid NDCG@10=0.5244, valid HR@10=0.7808
[Jul-24-2025_15-05-57] - epoch:32, time: 506.899468(s), valid (NDCG@10: 0.5275, HR@10: 0.7874), test: SKIPPED, all_time: 16204.628566(s)
[Jul-24-2025_15-05-57] - 新的最佳性能: valid NDCG@10=0.5275, valid HR@10=0.7874
[Jul-24-2025_15-14-53] - epoch:33, time: 505.753390(s), valid (NDCG@10: 0.5226, HR@10: 0.7805), test: SKIPPED, all_time: 16710.381957(s)
[Jul-24-2025_15-23-51] - epoch:34, time: 507.281792(s), valid (NDCG@10: 0.5257, HR@10: 0.7871), test: SKIPPED, all_time: 17217.663749(s)
[Jul-24-2025_15-32-50] - epoch:35, time: 507.810375(s), valid (NDCG@10: 0.5220, HR@10: 0.7808), test: SKIPPED, all_time: 17725.474124(s)
[Jul-24-2025_15-41-51] - epoch:36, time: 510.342134(s), valid (NDCG@10: 0.5270, HR@10: 0.7836), test: SKIPPED, all_time: 18235.816258(s)
[Jul-24-2025_15-50-48] - epoch:37, time: 506.661281(s), valid (NDCG@10: 0.5291, HR@10: 0.7825), test: SKIPPED, all_time: 18742.477539(s)
[Jul-24-2025_15-59-43] - epoch:38, time: 504.192836(s), valid (NDCG@10: 0.5276, HR@10: 0.7874), test: SKIPPED, all_time: 19246.670375(s)
[Jul-24-2025_16-08-37] - epoch:39, time: 503.826581(s), valid (NDCG@10: 0.5299, HR@10: 0.7854), test: SKIPPED, all_time: 19750.496956(s)
[Jul-24-2025_16-17-32] - epoch:40, time: 504.317030(s), valid (NDCG@10: 0.5289, HR@10: 0.7863), test: SKIPPED, all_time: 20254.813986(s)
[Jul-24-2025_16-26-24] - epoch:41, time: 501.289064(s), valid (NDCG@10: 0.5285, HR@10: 0.7773), test: SKIPPED, all_time: 20756.103050(s)
[Jul-24-2025_16-35-03] - epoch:42, time: 489.922241(s), valid (NDCG@10: 0.5266, HR@10: 0.7803), test: SKIPPED, all_time: 21246.025291(s)
[Jul-24-2025_16-43-41] - epoch:43, time: 487.703716(s), valid (NDCG@10: 0.5302, HR@10: 0.7841), test: SKIPPED, all_time: 21733.729006(s)
[Jul-24-2025_16-52-20] - epoch:44, time: 489.388108(s), valid (NDCG@10: 0.5292, HR@10: 0.7821), test: SKIPPED, all_time: 22223.117115(s)
[Jul-24-2025_17-01-01] - epoch:45, time: 491.097632(s), valid (NDCG@10: 0.5316, HR@10: 0.7798), test: SKIPPED, all_time: 22714.214746(s)
[Jul-24-2025_17-09-41] - epoch:46, time: 490.229436(s), valid (NDCG@10: 0.5301, HR@10: 0.7796), test: SKIPPED, all_time: 23204.444182(s)
[Jul-24-2025_17-18-24] - epoch:47, time: 492.920515(s), valid (NDCG@10: 0.5285, HR@10: 0.7829), test: SKIPPED, all_time: 23697.364697(s)
[Jul-24-2025_17-27-05] - epoch:48, time: 490.917631(s), valid (NDCG@10: 0.5297, HR@10: 0.7816), test: SKIPPED, all_time: 24188.282328(s)
[Jul-24-2025_17-35-49] - epoch:49, time: 493.891083(s), valid (NDCG@10: 0.5277, HR@10: 0.7778), test: SKIPPED, all_time: 24682.173411(s)
[Jul-24-2025_17-44-32] - epoch:50, time: 493.251675(s), valid (NDCG@10: 0.5310, HR@10: 0.7803), test: SKIPPED, all_time: 25175.425086(s)
[Jul-24-2025_17-53-15] - epoch:51, time: 492.767542(s), valid (NDCG@10: 0.5267, HR@10: 0.7760), test: SKIPPED, all_time: 25668.192628(s)
[Jul-24-2025_18-02-03] - epoch:52, time: 498.075632(s), valid (NDCG@10: 0.5318, HR@10: 0.7831), test: SKIPPED, all_time: 26166.268261(s)
[Jul-24-2025_18-11-02] - epoch:53, time: 507.582847(s), valid (NDCG@10: 0.5333, HR@10: 0.7801), test: SKIPPED, all_time: 26673.851107(s)
[Jul-24-2025_18-20-01] - epoch:54, time: 507.999161(s), valid (NDCG@10: 0.5292, HR@10: 0.7805), test: SKIPPED, all_time: 27181.850269(s)
[Jul-24-2025_18-28-58] - epoch:55, time: 506.958993(s), valid (NDCG@10: 0.5286, HR@10: 0.7805), test: SKIPPED, all_time: 27688.809262(s)
[Jul-24-2025_18-37-57] - epoch:56, time: 508.188260(s), valid (NDCG@10: 0.5342, HR@10: 0.7851), test: SKIPPED, all_time: 28196.997522(s)
[Jul-24-2025_18-46-56] - epoch:57, time: 507.446557(s), valid (NDCG@10: 0.5319, HR@10: 0.7858), test: SKIPPED, all_time: 28704.444079(s)
[Jul-24-2025_18-55-53] - epoch:58, time: 506.752493(s), valid (NDCG@10: 0.5278, HR@10: 0.7763), test: SKIPPED, all_time: 29211.196572(s)
[Jul-24-2025_19-04-50] - epoch:59, time: 506.140924(s), valid (NDCG@10: 0.5330, HR@10: 0.7793), test: SKIPPED, all_time: 29717.337497(s)
[Jul-24-2025_19-13-46] - epoch:60, time: 505.596589(s), valid (NDCG@10: 0.5282, HR@10: 0.7772), test: SKIPPED, all_time: 30222.934085(s)
[Jul-24-2025_19-22-42] - epoch:61, time: 506.166109(s), valid (NDCG@10: 0.5269, HR@10: 0.7773), test: SKIPPED, all_time: 30729.100194(s)
[Jul-24-2025_19-31-40] - epoch:62, time: 506.346194(s), valid (NDCG@10: 0.5249, HR@10: 0.7728), test: SKIPPED, all_time: 31235.446388(s)
[Jul-24-2025_19-40-40] - epoch:63, time: 509.905312(s), valid (NDCG@10: 0.5309, HR@10: 0.7800), test: SKIPPED, all_time: 31745.351701(s)
[Jul-24-2025_19-49-39] - epoch:64, time: 508.024942(s), valid (NDCG@10: 0.5326, HR@10: 0.7796), test: SKIPPED, all_time: 32253.376643(s)
[Jul-24-2025_19-58-38] - epoch:65, time: 508.536028(s), valid (NDCG@10: 0.5305, HR@10: 0.7790), test: SKIPPED, all_time: 32761.912671(s)
[Jul-24-2025_20-07-35] - epoch:66, time: 506.225442(s), valid (NDCG@10: 0.5283, HR@10: 0.7755), test: SKIPPED, all_time: 33268.138113(s)
[Jul-24-2025_20-16-31] - epoch:67, time: 505.536795(s), valid (NDCG@10: 0.5266, HR@10: 0.7748), test: SKIPPED, all_time: 33773.674907(s)
[Jul-24-2025_20-25-26] - epoch:68, time: 504.647065(s), valid (NDCG@10: 0.5285, HR@10: 0.7724), test: SKIPPED, all_time: 34278.321972(s)
[Jul-24-2025_20-34-23] - epoch:69, time: 505.941123(s), valid (NDCG@10: 0.5271, HR@10: 0.7745), test: SKIPPED, all_time: 34784.263095(s)
[Jul-24-2025_20-43-18] - epoch:70, time: 504.965777(s), valid (NDCG@10: 0.5240, HR@10: 0.7732), test: SKIPPED, all_time: 35289.228872(s)
[Jul-24-2025_20-52-14] - epoch:71, time: 504.997349(s), valid (NDCG@10: 0.5258, HR@10: 0.7743), test: SKIPPED, all_time: 35794.226221(s)
[Jul-24-2025_21-01-10] - epoch:72, time: 505.166680(s), valid (NDCG@10: 0.5294, HR@10: 0.7796), test: SKIPPED, all_time: 36299.392901(s)
[Jul-24-2025_21-10-06] - epoch:73, time: 505.926837(s), valid (NDCG@10: 0.5245, HR@10: 0.7730), test: SKIPPED, all_time: 36805.319738(s)
[Jul-24-2025_21-19-03] - epoch:74, time: 506.221386(s), valid (NDCG@10: 0.5275, HR@10: 0.7772), test: SKIPPED, all_time: 37311.541125(s)
[Jul-24-2025_21-27-58] - epoch:75, time: 505.037468(s), valid (NDCG@10: 0.5294, HR@10: 0.7800), test: SKIPPED, all_time: 37816.578593(s)
[Jul-24-2025_21-36-57] - epoch:76, time: 507.747797(s), valid (NDCG@10: 0.5222, HR@10: 0.7722), test: SKIPPED, all_time: 38324.326389(s)
[Jul-24-2025_21-45-53] - epoch:77, time: 505.693988(s), valid (NDCG@10: 0.5218, HR@10: 0.7730), test: SKIPPED, all_time: 38830.020377(s)
[Jul-24-2025_21-54-50] - epoch:78, time: 506.697666(s), valid (NDCG@10: 0.5191, HR@10: 0.7677), test: SKIPPED, all_time: 39336.718042(s)
[Jul-24-2025_22-03-47] - epoch:79, time: 506.173637(s), valid (NDCG@10: 0.5235, HR@10: 0.7735), test: SKIPPED, all_time: 39842.891680(s)
[Jul-24-2025_22-12-43] - epoch:80, time: 505.292303(s), valid (NDCG@10: 0.5260, HR@10: 0.7717), test: SKIPPED, all_time: 40348.183982(s)
[Jul-24-2025_22-21-38] - epoch:81, time: 505.556435(s), valid (NDCG@10: 0.5227, HR@10: 0.7752), test: SKIPPED, all_time: 40853.740417(s)
[Jul-24-2025_22-30-34] - epoch:82, time: 504.630636(s), valid (NDCG@10: 0.5230, HR@10: 0.7709), test: SKIPPED, all_time: 41358.371053(s)
[Jul-24-2025_22-39-31] - epoch:83, time: 506.048912(s), valid (NDCG@10: 0.5224, HR@10: 0.7758), test: SKIPPED, all_time: 41864.419965(s)
[Jul-24-2025_22-48-26] - epoch:84, time: 505.215933(s), valid (NDCG@10: 0.5252, HR@10: 0.7732), test: SKIPPED, all_time: 42369.635898(s)
[Jul-24-2025_22-57-23] - epoch:85, time: 506.190140(s), valid (NDCG@10: 0.5174, HR@10: 0.7742), test: SKIPPED, all_time: 42875.826038(s)
[Jul-24-2025_23-06-19] - epoch:86, time: 505.209610(s), valid (NDCG@10: 0.5226, HR@10: 0.7704), test: SKIPPED, all_time: 43381.035647(s)
[Jul-24-2025_23-15-15] - epoch:87, time: 505.249969(s), valid (NDCG@10: 0.5222, HR@10: 0.7699), test: SKIPPED, all_time: 43886.285617(s)
[Jul-24-2025_23-24-13] - epoch:88, time: 507.174520(s), valid (NDCG@10: 0.5157, HR@10: 0.7671), test: SKIPPED, all_time: 44393.460136(s)
[Jul-24-2025_23-33-10] - epoch:89, time: 507.111579(s), valid (NDCG@10: 0.5187, HR@10: 0.7714), test: SKIPPED, all_time: 44900.571715(s)
[Jul-24-2025_23-42-07] - epoch:90, time: 505.988169(s), valid (NDCG@10: 0.5197, HR@10: 0.7709), test: SKIPPED, all_time: 45406.559884(s)
[Jul-24-2025_23-51-03] - epoch:91, time: 505.520275(s), valid (NDCG@10: 0.5215, HR@10: 0.7664), test: SKIPPED, all_time: 45912.080159(s)
[Jul-25-2025_00-00-00] - epoch:92, time: 506.793668(s), valid (NDCG@10: 0.5253, HR@10: 0.7704), test: SKIPPED, all_time: 46418.873827(s)
[Jul-25-2025_00-08-57] - epoch:93, time: 506.547580(s), valid (NDCG@10: 0.5156, HR@10: 0.7649), test: SKIPPED, all_time: 46925.421407(s)
[Jul-25-2025_00-17-56] - epoch:94, time: 505.950080(s), valid (NDCG@10: 0.5205, HR@10: 0.7672), test: SKIPPED, all_time: 47431.371487(s)
[Jul-25-2025_00-26-57] - epoch:95, time: 510.895087(s), valid (NDCG@10: 0.5128, HR@10: 0.7619), test: SKIPPED, all_time: 47942.266574(s)
[Jul-25-2025_00-35-53] - epoch:96, time: 505.594693(s), valid (NDCG@10: 0.5179, HR@10: 0.7636), test: SKIPPED, all_time: 48447.861267(s)
[Jul-25-2025_00-44-49] - epoch:97, time: 505.025140(s), valid (NDCG@10: 0.5210, HR@10: 0.7619), test: SKIPPED, all_time: 48952.886408(s)
[Jul-25-2025_00-53-45] - epoch:98, time: 505.095434(s), valid (NDCG@10: 0.5176, HR@10: 0.7695), test: SKIPPED, all_time: 49457.981842(s)
[Jul-25-2025_01-02-40] - epoch:99, time: 504.035662(s), valid (NDCG@10: 0.5160, HR@10: 0.7699), test: SKIPPED, all_time: 49962.017504(s)
[Jul-25-2025_01-11-34] - epoch:100, time: 504.062909(s), valid (NDCG@10: 0.5135, HR@10: 0.7614), test: SKIPPED, all_time: 50466.080413(s)
[Jul-25-2025_01-20-29] - epoch:101, time: 504.659401(s), valid (NDCG@10: 0.5116, HR@10: 0.7608), test: SKIPPED, all_time: 50970.739814(s)
[Jul-25-2025_01-29-24] - epoch:102, time: 503.532666(s), valid (NDCG@10: 0.5131, HR@10: 0.7608), test: SKIPPED, all_time: 51474.272480(s)
[Jul-25-2025_01-38-20] - epoch:103, time: 506.240155(s), valid (NDCG@10: 0.5149, HR@10: 0.7558), test: SKIPPED, all_time: 51980.512635(s)
[Jul-25-2025_01-47-14] - epoch:104, time: 503.613158(s), valid (NDCG@10: 0.5157, HR@10: 0.7614), test: SKIPPED, all_time: 52484.125793(s)
[Jul-25-2025_01-56-11] - epoch:105, time: 506.819941(s), valid (NDCG@10: 0.5169, HR@10: 0.7629), test: SKIPPED, all_time: 52990.945734(s)
[Jul-25-2025_02-05-07] - 早停触发！NDCG在50轮内没有改善。
[Jul-25-2025_02-05-07] - epoch:106, time: 505.233881(s), valid (NDCG@10: 0.5110, HR@10: 0.7613), test: SKIPPED, all_time: 53496.179615(s)
[Jul-25-2025_02-05-07] - [联邦训练] 最佳结果: valid NDCG@10=0.5342, HR@10=0.7874 (测试集评估已跳过)
