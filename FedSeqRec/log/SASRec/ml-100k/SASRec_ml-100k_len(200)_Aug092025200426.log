{'early_stop_enabled': True, 'model': 'SASRec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000, 'early_stop': 30, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': False, 'eval_freq': 1, 'c': 9, 'alpha': 0.3}
[Aug-09-2025_20-04-26] - 开始训练，配置参数如下：
[Aug-09-2025_20-04-26] - early_stop_enabled: True
[Aug-09-2025_20-04-26] - model: SASRec
[Aug-09-2025_20-04-26] - lr: 0.001
[Aug-09-2025_20-04-26] - batch_size: 128
[Aug-09-2025_20-04-26] - neg_num: 99
[Aug-09-2025_20-04-26] - l2_reg: 0
[Aug-09-2025_20-04-26] - l2_emb: 0.0
[Aug-09-2025_20-04-26] - hidden_size: 50
[Aug-09-2025_20-04-26] - dropout: 0.2
[Aug-09-2025_20-04-26] - epochs: 1000
[Aug-09-2025_20-04-26] - early_stop: 30
[Aug-09-2025_20-04-26] - datapath: ../../data/
[Aug-09-2025_20-04-26] - dataset: ml-100k
[Aug-09-2025_20-04-26] - train_data: ml-100k.txt
[Aug-09-2025_20-04-26] - log_path: ../log
[Aug-09-2025_20-04-26] - num_layers: 2
[Aug-09-2025_20-04-26] - num_heads: 1
[Aug-09-2025_20-04-26] - inner_size: 256
[Aug-09-2025_20-04-26] - max_seq_len: 200
[Aug-09-2025_20-04-26] - upload_mode: full
[Aug-09-2025_20-04-26] - skip_test_eval: False
[Aug-09-2025_20-04-26] - eval_freq: 1
[Aug-09-2025_20-04-26] - c: 9
[Aug-09-2025_20-04-26] - alpha: 0.3
[Aug-09-2025_20-04-26] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-09-2025_20-04-26] - 最大序列长度: 200
[Aug-09-2025_20-04-26] - 批次大小: 128
[Aug-09-2025_20-04-28] - 参数上传模式: full
[Aug-09-2025_20-04-28] - 隐私参数（本地更新）: []
[Aug-09-2025_20-04-28] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'trm_encoder.layer.0.multi_head_attention.query.weight', 'trm_encoder.layer.0.multi_head_attention.query.bias', 'trm_encoder.layer.0.multi_head_attention.key.weight', 'trm_encoder.layer.0.multi_head_attention.key.bias', 'trm_encoder.layer.0.multi_head_attention.value.weight', 'trm_encoder.layer.0.multi_head_attention.value.bias', 'trm_encoder.layer.0.multi_head_attention.dense.weight', 'trm_encoder.layer.0.multi_head_attention.dense.bias', 'trm_encoder.layer.0.multi_head_attention.LayerNorm.weight', 'trm_encoder.layer.0.multi_head_attention.LayerNorm.bias', 'trm_encoder.layer.0.feed_forward.dense_1.weight', 'trm_encoder.layer.0.feed_forward.dense_1.bias', 'trm_encoder.layer.0.feed_forward.dense_2.weight', 'trm_encoder.layer.0.feed_forward.dense_2.bias', 'trm_encoder.layer.0.feed_forward.LayerNorm.weight', 'trm_encoder.layer.0.feed_forward.LayerNorm.bias', 'trm_encoder.layer.1.multi_head_attention.query.weight', 'trm_encoder.layer.1.multi_head_attention.query.bias', 'trm_encoder.layer.1.multi_head_attention.key.weight', 'trm_encoder.layer.1.multi_head_attention.key.bias', 'trm_encoder.layer.1.multi_head_attention.value.weight', 'trm_encoder.layer.1.multi_head_attention.value.bias', 'trm_encoder.layer.1.multi_head_attention.dense.weight', 'trm_encoder.layer.1.multi_head_attention.dense.bias', 'trm_encoder.layer.1.multi_head_attention.LayerNorm.weight', 'trm_encoder.layer.1.multi_head_attention.LayerNorm.bias', 'trm_encoder.layer.1.feed_forward.dense_1.weight', 'trm_encoder.layer.1.feed_forward.dense_1.bias', 'trm_encoder.layer.1.feed_forward.dense_2.weight', 'trm_encoder.layer.1.feed_forward.dense_2.bias', 'trm_encoder.layer.1.feed_forward.LayerNorm.weight', 'trm_encoder.layer.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-09-2025_20-04-28] - 用户数量: 943
[Aug-09-2025_20-04-28] - 物品数量: 1349
[Aug-09-2025_20-04-30] - Epoch 1  用户参数总大小为: 75131.00 KB
[Aug-09-2025_20-04-54] - epoch:1, time: 26.566589(s), valid (NDCG@10: 0.1201, HR@10: 0.2524), test (NDCG@10: 0.1271, HR@10: 0.2577) all_time: 26.566589(s)
[Aug-09-2025_20-04-54] - 新的最佳性能: valid NDCG@10=0.1201, test NDCG@10=0.1271
[Aug-09-2025_20-04-56] - Epoch 2  用户参数总大小为: 75131.00 KB
