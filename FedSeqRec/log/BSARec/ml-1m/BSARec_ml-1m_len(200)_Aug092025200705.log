{'early_stop_enabled': True, 'model': 'BSARec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000, 'early_stop': 30, 'datapath': '../../data/', 'dataset': 'ml-1m', 'train_data': 'ml-1m.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': False, 'eval_freq': 1, 'c': 9, 'alpha': 0.3}
[Aug-09-2025_20-07-05] - 开始训练，配置参数如下：
[Aug-09-2025_20-07-05] - early_stop_enabled: True
[Aug-09-2025_20-07-05] - model: BSARec
[Aug-09-2025_20-07-05] - lr: 0.001
[Aug-09-2025_20-07-05] - batch_size: 128
[Aug-09-2025_20-07-05] - neg_num: 99
[Aug-09-2025_20-07-05] - l2_reg: 0
[Aug-09-2025_20-07-05] - l2_emb: 0.0
[Aug-09-2025_20-07-05] - hidden_size: 50
[Aug-09-2025_20-07-05] - dropout: 0.2
[Aug-09-2025_20-07-05] - epochs: 1000
[Aug-09-2025_20-07-05] - early_stop: 30
[Aug-09-2025_20-07-05] - datapath: ../../data/
[Aug-09-2025_20-07-05] - dataset: ml-1m
[Aug-09-2025_20-07-05] - train_data: ml-1m.txt
[Aug-09-2025_20-07-05] - log_path: ../log
[Aug-09-2025_20-07-05] - num_layers: 2
[Aug-09-2025_20-07-05] - num_heads: 1
[Aug-09-2025_20-07-05] - inner_size: 256
[Aug-09-2025_20-07-05] - max_seq_len: 200
[Aug-09-2025_20-07-05] - upload_mode: full
[Aug-09-2025_20-07-05] - skip_test_eval: False
[Aug-09-2025_20-07-05] - eval_freq: 1
[Aug-09-2025_20-07-05] - c: 9
[Aug-09-2025_20-07-05] - alpha: 0.3
[Aug-09-2025_20-07-05] - 训练数据: ../../data/ml-1m/ml-1m.txt
[Aug-09-2025_20-07-05] - 最大序列长度: 200
[Aug-09-2025_20-07-05] - 批次大小: 128
[Aug-09-2025_20-07-08] - 参数上传模式: full
[Aug-09-2025_20-07-08] - 隐私参数（本地更新）: []
[Aug-09-2025_20-07-08] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'encoder.blocks.0.layer.filter_layer.sqrt_beta', 'encoder.blocks.0.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.0.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.0.layer.attention_layer.query.weight', 'encoder.blocks.0.layer.attention_layer.query.bias', 'encoder.blocks.0.layer.attention_layer.key.weight', 'encoder.blocks.0.layer.attention_layer.key.bias', 'encoder.blocks.0.layer.attention_layer.value.weight', 'encoder.blocks.0.layer.attention_layer.value.bias', 'encoder.blocks.0.layer.attention_layer.dense.weight', 'encoder.blocks.0.layer.attention_layer.dense.bias', 'encoder.blocks.0.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.0.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.0.feed_forward.dense_1.weight', 'encoder.blocks.0.feed_forward.dense_1.bias', 'encoder.blocks.0.feed_forward.dense_2.weight', 'encoder.blocks.0.feed_forward.dense_2.bias', 'encoder.blocks.0.feed_forward.LayerNorm.weight', 'encoder.blocks.0.feed_forward.LayerNorm.bias', 'encoder.blocks.1.layer.filter_layer.sqrt_beta', 'encoder.blocks.1.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.1.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.1.layer.attention_layer.query.weight', 'encoder.blocks.1.layer.attention_layer.query.bias', 'encoder.blocks.1.layer.attention_layer.key.weight', 'encoder.blocks.1.layer.attention_layer.key.bias', 'encoder.blocks.1.layer.attention_layer.value.weight', 'encoder.blocks.1.layer.attention_layer.value.bias', 'encoder.blocks.1.layer.attention_layer.dense.weight', 'encoder.blocks.1.layer.attention_layer.dense.bias', 'encoder.blocks.1.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.1.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.1.feed_forward.dense_1.weight', 'encoder.blocks.1.feed_forward.dense_1.bias', 'encoder.blocks.1.feed_forward.dense_2.weight', 'encoder.blocks.1.feed_forward.dense_2.bias', 'encoder.blocks.1.feed_forward.LayerNorm.weight', 'encoder.blocks.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-09-2025_20-07-08] - 用户数量: 6040
[Aug-09-2025_20-07-08] - 物品数量: 3416
[Aug-09-2025_20-07-10] -  用户参数总大小为: 126956.00 KB
