{'early_stop_enabled': True, 'model': 'BSARec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000, 'early_stop': 30, 'datapath': '../../data/', 'dataset': 'ml-1m', 'train_data': 'ml-1m.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'c': 9, 'alpha': 0.7}
[Aug-06-2025_01-19-00] - 开始训练，配置参数如下：
[Aug-06-2025_01-19-00] - early_stop_enabled: True
[Aug-06-2025_01-19-00] - model: BSARec
[Aug-06-2025_01-19-00] - lr: 0.001
[Aug-06-2025_01-19-00] - batch_size: 128
[Aug-06-2025_01-19-00] - neg_num: 99
[Aug-06-2025_01-19-00] - l2_reg: 0
[Aug-06-2025_01-19-00] - l2_emb: 0.0
[Aug-06-2025_01-19-00] - hidden_size: 50
[Aug-06-2025_01-19-00] - dropout: 0.2
[Aug-06-2025_01-19-00] - epochs: 1000
[Aug-06-2025_01-19-00] - early_stop: 30
[Aug-06-2025_01-19-00] - datapath: ../../data/
[Aug-06-2025_01-19-00] - dataset: ml-1m
[Aug-06-2025_01-19-00] - train_data: ml-1m.txt
[Aug-06-2025_01-19-00] - log_path: ../log
[Aug-06-2025_01-19-00] - num_layers: 2
[Aug-06-2025_01-19-00] - num_heads: 1
[Aug-06-2025_01-19-00] - inner_size: 256
[Aug-06-2025_01-19-00] - max_seq_len: 200
[Aug-06-2025_01-19-00] - upload_mode: full
[Aug-06-2025_01-19-00] - skip_test_eval: True
[Aug-06-2025_01-19-00] - eval_freq: 1
[Aug-06-2025_01-19-00] - c: 9
[Aug-06-2025_01-19-00] - alpha: 0.7
[Aug-06-2025_01-19-00] - 训练数据: ../../data/ml-1m/ml-1m.txt
[Aug-06-2025_01-19-00] - 最大序列长度: 200
[Aug-06-2025_01-19-00] - 批次大小: 128
[Aug-06-2025_01-19-05] - 参数上传模式: full
[Aug-06-2025_01-19-05] - 隐私参数（本地更新）: []
[Aug-06-2025_01-19-05] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'encoder.blocks.0.layer.filter_layer.sqrt_beta', 'encoder.blocks.0.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.0.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.0.layer.attention_layer.query.weight', 'encoder.blocks.0.layer.attention_layer.query.bias', 'encoder.blocks.0.layer.attention_layer.key.weight', 'encoder.blocks.0.layer.attention_layer.key.bias', 'encoder.blocks.0.layer.attention_layer.value.weight', 'encoder.blocks.0.layer.attention_layer.value.bias', 'encoder.blocks.0.layer.attention_layer.dense.weight', 'encoder.blocks.0.layer.attention_layer.dense.bias', 'encoder.blocks.0.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.0.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.0.feed_forward.dense_1.weight', 'encoder.blocks.0.feed_forward.dense_1.bias', 'encoder.blocks.0.feed_forward.dense_2.weight', 'encoder.blocks.0.feed_forward.dense_2.bias', 'encoder.blocks.0.feed_forward.LayerNorm.weight', 'encoder.blocks.0.feed_forward.LayerNorm.bias', 'encoder.blocks.1.layer.filter_layer.sqrt_beta', 'encoder.blocks.1.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.1.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.1.layer.attention_layer.query.weight', 'encoder.blocks.1.layer.attention_layer.query.bias', 'encoder.blocks.1.layer.attention_layer.key.weight', 'encoder.blocks.1.layer.attention_layer.key.bias', 'encoder.blocks.1.layer.attention_layer.value.weight', 'encoder.blocks.1.layer.attention_layer.value.bias', 'encoder.blocks.1.layer.attention_layer.dense.weight', 'encoder.blocks.1.layer.attention_layer.dense.bias', 'encoder.blocks.1.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.1.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.1.feed_forward.dense_1.weight', 'encoder.blocks.1.feed_forward.dense_1.bias', 'encoder.blocks.1.feed_forward.dense_2.weight', 'encoder.blocks.1.feed_forward.dense_2.bias', 'encoder.blocks.1.feed_forward.LayerNorm.weight', 'encoder.blocks.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-06-2025_01-19-05] - 用户数量: 6040
[Aug-06-2025_01-19-05] - 物品数量: 3416
[Aug-06-2025_01-23-10] - epoch:1, time: 244.916727(s), valid (NDCG@10: 0.2410, HR@10: 0.4387), test: SKIPPED, all_time: 244.916727(s)
[Aug-06-2025_01-23-10] - 新的最佳性能: valid NDCG@10=0.2410, valid HR@10=0.4387
[Aug-06-2025_01-27-25] - epoch:2, time: 255.421555(s), valid (NDCG@10: 0.2539, HR@10: 0.4603), test: SKIPPED, all_time: 500.338281(s)
[Aug-06-2025_01-27-25] - 新的最佳性能: valid NDCG@10=0.2539, valid HR@10=0.4603
[Aug-06-2025_01-31-38] - epoch:3, time: 253.238961(s), valid (NDCG@10: 0.3387, HR@10: 0.5752), test: SKIPPED, all_time: 753.577243(s)
[Aug-06-2025_01-31-38] - 新的最佳性能: valid NDCG@10=0.3387, valid HR@10=0.5752
[Aug-06-2025_01-35-53] - epoch:4, time: 254.608612(s), valid (NDCG@10: 0.4036, HR@10: 0.6603), test: SKIPPED, all_time: 1008.185855(s)
[Aug-06-2025_01-35-53] - 新的最佳性能: valid NDCG@10=0.4036, valid HR@10=0.6603
[Aug-06-2025_01-40-10] - epoch:5, time: 257.222668(s), valid (NDCG@10: 0.4348, HR@10: 0.6912), test: SKIPPED, all_time: 1265.408523(s)
[Aug-06-2025_01-40-10] - 新的最佳性能: valid NDCG@10=0.4348, valid HR@10=0.6912
[Aug-06-2025_01-44-28] - epoch:6, time: 257.501307(s), valid (NDCG@10: 0.4611, HR@10: 0.7224), test: SKIPPED, all_time: 1522.909830(s)
[Aug-06-2025_01-44-28] - 新的最佳性能: valid NDCG@10=0.4611, valid HR@10=0.7224
[Aug-06-2025_01-48-43] - epoch:7, time: 255.381267(s), valid (NDCG@10: 0.4871, HR@10: 0.7472), test: SKIPPED, all_time: 1778.291096(s)
[Aug-06-2025_01-48-43] - 新的最佳性能: valid NDCG@10=0.4871, valid HR@10=0.7472
[Aug-06-2025_01-53-00] - epoch:8, time: 257.242713(s), valid (NDCG@10: 0.5005, HR@10: 0.7535), test: SKIPPED, all_time: 2035.533809(s)
[Aug-06-2025_01-53-00] - 新的最佳性能: valid NDCG@10=0.5005, valid HR@10=0.7535
[Aug-06-2025_01-57-17] - epoch:9, time: 256.585362(s), valid (NDCG@10: 0.5149, HR@10: 0.7593), test: SKIPPED, all_time: 2292.119171(s)
[Aug-06-2025_01-57-17] - 新的最佳性能: valid NDCG@10=0.5149, valid HR@10=0.7593
[Aug-06-2025_02-01-33] - epoch:10, time: 256.747173(s), valid (NDCG@10: 0.5230, HR@10: 0.7659), test: SKIPPED, all_time: 2548.866344(s)
[Aug-06-2025_02-01-33] - 新的最佳性能: valid NDCG@10=0.5230, valid HR@10=0.7659
[Aug-06-2025_02-05-50] - epoch:11, time: 256.898260(s), valid (NDCG@10: 0.5298, HR@10: 0.7727), test: SKIPPED, all_time: 2805.764604(s)
[Aug-06-2025_02-05-50] - 新的最佳性能: valid NDCG@10=0.5298, valid HR@10=0.7727
[Aug-06-2025_02-10-07] - epoch:12, time: 256.378247(s), valid (NDCG@10: 0.5324, HR@10: 0.7719), test: SKIPPED, all_time: 3062.142852(s)
[Aug-06-2025_02-14-20] - epoch:13, time: 253.328059(s), valid (NDCG@10: 0.5368, HR@10: 0.7722), test: SKIPPED, all_time: 3315.470911(s)
[Aug-06-2025_02-18-38] - epoch:14, time: 257.750362(s), valid (NDCG@10: 0.5397, HR@10: 0.7753), test: SKIPPED, all_time: 3573.221273(s)
[Aug-06-2025_02-18-38] - 新的最佳性能: valid NDCG@10=0.5397, valid HR@10=0.7753
[Aug-06-2025_02-22-51] - epoch:15, time: 252.856560(s), valid (NDCG@10: 0.5371, HR@10: 0.7661), test: SKIPPED, all_time: 3826.077833(s)
[Aug-06-2025_02-27-04] - epoch:16, time: 253.120873(s), valid (NDCG@10: 0.5426, HR@10: 0.7748), test: SKIPPED, all_time: 4079.198707(s)
[Aug-06-2025_02-31-18] - epoch:17, time: 254.430788(s), valid (NDCG@10: 0.5427, HR@10: 0.7770), test: SKIPPED, all_time: 4333.629495(s)
[Aug-06-2025_02-31-18] - 新的最佳性能: valid NDCG@10=0.5427, valid HR@10=0.7770
[Aug-06-2025_02-35-31] - epoch:18, time: 252.545821(s), valid (NDCG@10: 0.5454, HR@10: 0.7753), test: SKIPPED, all_time: 4586.175316(s)
[Aug-06-2025_02-39-44] - epoch:19, time: 253.286227(s), valid (NDCG@10: 0.5467, HR@10: 0.7742), test: SKIPPED, all_time: 4839.461543(s)
[Aug-06-2025_02-43-58] - epoch:20, time: 254.187538(s), valid (NDCG@10: 0.5483, HR@10: 0.7745), test: SKIPPED, all_time: 5093.649081(s)
[Aug-06-2025_02-48-11] - epoch:21, time: 252.355813(s), valid (NDCG@10: 0.5465, HR@10: 0.7750), test: SKIPPED, all_time: 5346.004895(s)
[Aug-06-2025_02-52-24] - epoch:22, time: 253.441087(s), valid (NDCG@10: 0.5506, HR@10: 0.7781), test: SKIPPED, all_time: 5599.445982(s)
[Aug-06-2025_02-52-24] - 新的最佳性能: valid NDCG@10=0.5506, valid HR@10=0.7781
[Aug-06-2025_02-56-38] - epoch:23, time: 254.146891(s), valid (NDCG@10: 0.5489, HR@10: 0.7750), test: SKIPPED, all_time: 5853.592873(s)
[Aug-06-2025_03-00-52] - epoch:24, time: 253.388525(s), valid (NDCG@10: 0.5543, HR@10: 0.7772), test: SKIPPED, all_time: 6106.981397(s)
[Aug-06-2025_03-05-06] - epoch:25, time: 254.086766(s), valid (NDCG@10: 0.5469, HR@10: 0.7740), test: SKIPPED, all_time: 6361.068163(s)
[Aug-06-2025_03-09-19] - epoch:26, time: 253.794549(s), valid (NDCG@10: 0.5491, HR@10: 0.7720), test: SKIPPED, all_time: 6614.862711(s)
[Aug-06-2025_03-13-33] - epoch:27, time: 253.733686(s), valid (NDCG@10: 0.5493, HR@10: 0.7722), test: SKIPPED, all_time: 6868.596398(s)
[Aug-06-2025_03-17-47] - epoch:28, time: 253.750268(s), valid (NDCG@10: 0.5505, HR@10: 0.7722), test: SKIPPED, all_time: 7122.346666(s)
[Aug-06-2025_03-22-01] - epoch:29, time: 253.570343(s), valid (NDCG@10: 0.5515, HR@10: 0.7727), test: SKIPPED, all_time: 7375.917009(s)
[Aug-06-2025_03-26-14] - epoch:30, time: 253.899348(s), valid (NDCG@10: 0.5465, HR@10: 0.7722), test: SKIPPED, all_time: 7629.816358(s)
[Aug-06-2025_03-30-30] - epoch:31, time: 255.693682(s), valid (NDCG@10: 0.5507, HR@10: 0.7728), test: SKIPPED, all_time: 7885.510040(s)
[Aug-06-2025_03-34-45] - epoch:32, time: 254.393188(s), valid (NDCG@10: 0.5481, HR@10: 0.7720), test: SKIPPED, all_time: 8139.903227(s)
[Aug-06-2025_03-38-59] - epoch:33, time: 254.232093(s), valid (NDCG@10: 0.5463, HR@10: 0.7717), test: SKIPPED, all_time: 8394.135320(s)
[Aug-06-2025_03-43-13] - epoch:34, time: 254.268511(s), valid (NDCG@10: 0.5460, HR@10: 0.7712), test: SKIPPED, all_time: 8648.403831(s)
[Aug-06-2025_03-47-27] - epoch:35, time: 253.616695(s), valid (NDCG@10: 0.5446, HR@10: 0.7692), test: SKIPPED, all_time: 8902.020526(s)
[Aug-06-2025_03-51-40] - epoch:36, time: 253.324628(s), valid (NDCG@10: 0.5465, HR@10: 0.7692), test: SKIPPED, all_time: 9155.345154(s)
[Aug-06-2025_03-55-54] - epoch:37, time: 253.788847(s), valid (NDCG@10: 0.5419, HR@10: 0.7652), test: SKIPPED, all_time: 9409.134001(s)
[Aug-06-2025_04-00-08] - epoch:38, time: 254.353342(s), valid (NDCG@10: 0.5434, HR@10: 0.7631), test: SKIPPED, all_time: 9663.487343(s)
[Aug-06-2025_04-04-12] - epoch:39, time: 244.209383(s), valid (NDCG@10: 0.5395, HR@10: 0.7603), test: SKIPPED, all_time: 9907.696726(s)
[Aug-06-2025_04-07-24] - epoch:40, time: 191.450831(s), valid (NDCG@10: 0.5353, HR@10: 0.7629), test: SKIPPED, all_time: 10099.147557(s)
[Aug-06-2025_04-10-30] - epoch:41, time: 186.445660(s), valid (NDCG@10: 0.5373, HR@10: 0.7616), test: SKIPPED, all_time: 10285.593217(s)
[Aug-06-2025_04-13-30] - epoch:42, time: 179.538256(s), valid (NDCG@10: 0.5373, HR@10: 0.7589), test: SKIPPED, all_time: 10465.131473(s)
[Aug-06-2025_04-16-25] - epoch:43, time: 174.986276(s), valid (NDCG@10: 0.5376, HR@10: 0.7634), test: SKIPPED, all_time: 10640.117748(s)
[Aug-06-2025_04-19-18] - epoch:44, time: 173.163064(s), valid (NDCG@10: 0.5341, HR@10: 0.7589), test: SKIPPED, all_time: 10813.280812(s)
[Aug-06-2025_04-22-13] - epoch:45, time: 174.852717(s), valid (NDCG@10: 0.5346, HR@10: 0.7576), test: SKIPPED, all_time: 10988.133529(s)
[Aug-06-2025_04-25-10] - epoch:46, time: 177.545138(s), valid (NDCG@10: 0.5264, HR@10: 0.7513), test: SKIPPED, all_time: 11165.678667(s)
[Aug-06-2025_04-28-08] - epoch:47, time: 177.807051(s), valid (NDCG@10: 0.5330, HR@10: 0.7536), test: SKIPPED, all_time: 11343.485718(s)
[Aug-06-2025_04-30-54] - epoch:48, time: 165.472802(s), valid (NDCG@10: 0.5279, HR@10: 0.7472), test: SKIPPED, all_time: 11508.958520(s)
[Aug-06-2025_04-33-29] - epoch:49, time: 155.689775(s), valid (NDCG@10: 0.5228, HR@10: 0.7510), test: SKIPPED, all_time: 11664.648295(s)
[Aug-06-2025_04-35-59] - epoch:50, time: 149.971633(s), valid (NDCG@10: 0.5216, HR@10: 0.7452), test: SKIPPED, all_time: 11814.619928(s)
[Aug-06-2025_04-38-37] - epoch:51, time: 157.805956(s), valid (NDCG@10: 0.5252, HR@10: 0.7508), test: SKIPPED, all_time: 11972.425884(s)
[Aug-06-2025_04-41-02] - epoch:52, time: 145.266889(s), valid (NDCG@10: 0.5186, HR@10: 0.7454), test: SKIPPED, all_time: 12117.692773(s)
[Aug-06-2025_04-43-34] - epoch:53, time: 151.826945(s), valid (NDCG@10: 0.5227, HR@10: 0.7452), test: SKIPPED, all_time: 12269.519718(s)
[Aug-06-2025_04-45-55] - 早停触发！NDCG在30轮内没有改善。
[Aug-06-2025_04-45-55] - epoch:54, time: 141.274890(s), valid (NDCG@10: 0.5180, HR@10: 0.7401), test: SKIPPED, all_time: 12410.794608(s)
[Aug-06-2025_04-45-55] - [联邦训练] 最佳结果: valid NDCG@10=0.5543, HR@10=0.7781 (测试集评估已跳过)
