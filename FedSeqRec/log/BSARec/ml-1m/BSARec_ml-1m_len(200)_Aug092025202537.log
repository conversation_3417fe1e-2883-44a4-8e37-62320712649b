{'early_stop_enabled': True, 'model': 'BSARec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000, 'early_stop': 30, 'datapath': '../../data/', 'dataset': 'ml-1m', 'train_data': 'ml-1m.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': False, 'eval_freq': 1, 'c': 9, 'alpha': 0.3}
[Aug-09-2025_20-25-37] - 开始训练，配置参数如下：
[Aug-09-2025_20-25-37] - early_stop_enabled: True
[Aug-09-2025_20-25-37] - model: BSARec
[Aug-09-2025_20-25-37] - lr: 0.001
[Aug-09-2025_20-25-37] - batch_size: 128
[Aug-09-2025_20-25-37] - neg_num: 99
[Aug-09-2025_20-25-37] - l2_reg: 0
[Aug-09-2025_20-25-37] - l2_emb: 0.0
[Aug-09-2025_20-25-37] - hidden_size: 50
[Aug-09-2025_20-25-37] - dropout: 0.2
[Aug-09-2025_20-25-37] - epochs: 1000
[Aug-09-2025_20-25-37] - early_stop: 30
[Aug-09-2025_20-25-37] - datapath: ../../data/
[Aug-09-2025_20-25-37] - dataset: ml-1m
[Aug-09-2025_20-25-37] - train_data: ml-1m.txt
[Aug-09-2025_20-25-37] - log_path: ../log
[Aug-09-2025_20-25-37] - num_layers: 2
[Aug-09-2025_20-25-37] - num_heads: 1
[Aug-09-2025_20-25-37] - inner_size: 256
[Aug-09-2025_20-25-37] - max_seq_len: 200
[Aug-09-2025_20-25-37] - upload_mode: full
[Aug-09-2025_20-25-37] - skip_test_eval: False
[Aug-09-2025_20-25-37] - eval_freq: 1
[Aug-09-2025_20-25-37] - c: 9
[Aug-09-2025_20-25-37] - alpha: 0.3
[Aug-09-2025_20-25-37] - 训练数据: ../../data/ml-1m/ml-1m.txt
[Aug-09-2025_20-25-37] - 最大序列长度: 200
[Aug-09-2025_20-25-37] - 批次大小: 128
[Aug-09-2025_20-25-41] - 参数上传模式: full
[Aug-09-2025_20-25-41] - 隐私参数（本地更新）: []
[Aug-09-2025_20-25-41] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'encoder.blocks.0.layer.filter_layer.sqrt_beta', 'encoder.blocks.0.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.0.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.0.layer.attention_layer.query.weight', 'encoder.blocks.0.layer.attention_layer.query.bias', 'encoder.blocks.0.layer.attention_layer.key.weight', 'encoder.blocks.0.layer.attention_layer.key.bias', 'encoder.blocks.0.layer.attention_layer.value.weight', 'encoder.blocks.0.layer.attention_layer.value.bias', 'encoder.blocks.0.layer.attention_layer.dense.weight', 'encoder.blocks.0.layer.attention_layer.dense.bias', 'encoder.blocks.0.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.0.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.0.feed_forward.dense_1.weight', 'encoder.blocks.0.feed_forward.dense_1.bias', 'encoder.blocks.0.feed_forward.dense_2.weight', 'encoder.blocks.0.feed_forward.dense_2.bias', 'encoder.blocks.0.feed_forward.LayerNorm.weight', 'encoder.blocks.0.feed_forward.LayerNorm.bias', 'encoder.blocks.1.layer.filter_layer.sqrt_beta', 'encoder.blocks.1.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.1.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.1.layer.attention_layer.query.weight', 'encoder.blocks.1.layer.attention_layer.query.bias', 'encoder.blocks.1.layer.attention_layer.key.weight', 'encoder.blocks.1.layer.attention_layer.key.bias', 'encoder.blocks.1.layer.attention_layer.value.weight', 'encoder.blocks.1.layer.attention_layer.value.bias', 'encoder.blocks.1.layer.attention_layer.dense.weight', 'encoder.blocks.1.layer.attention_layer.dense.bias', 'encoder.blocks.1.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.1.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.1.feed_forward.dense_1.weight', 'encoder.blocks.1.feed_forward.dense_1.bias', 'encoder.blocks.1.feed_forward.dense_2.weight', 'encoder.blocks.1.feed_forward.dense_2.bias', 'encoder.blocks.1.feed_forward.LayerNorm.weight', 'encoder.blocks.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-09-2025_20-25-41] - 用户数量: 6040
[Aug-09-2025_20-25-41] - 物品数量: 3416
[Aug-09-2025_20-25-45] -  一个用户平均参数大小为: 1015648.0000 B
