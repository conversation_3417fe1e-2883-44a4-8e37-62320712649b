{'early_stop_enabled': True, 'model': 'BSARec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000, 'early_stop': 30, 'datapath': '../../data/', 'dataset': 'ml-1m', 'train_data': 'ml-1m.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'c': 9, 'alpha': 0.3}
[Aug-06-2025_09-58-07] - 开始训练，配置参数如下：
[Aug-06-2025_09-58-07] - early_stop_enabled: True
[Aug-06-2025_09-58-07] - model: BSARec
[Aug-06-2025_09-58-07] - lr: 0.001
[Aug-06-2025_09-58-07] - batch_size: 128
[Aug-06-2025_09-58-07] - neg_num: 99
[Aug-06-2025_09-58-07] - l2_reg: 0
[Aug-06-2025_09-58-07] - l2_emb: 0.0
[Aug-06-2025_09-58-07] - hidden_size: 50
[Aug-06-2025_09-58-07] - dropout: 0.2
[Aug-06-2025_09-58-07] - epochs: 1000
[Aug-06-2025_09-58-07] - early_stop: 30
[Aug-06-2025_09-58-07] - datapath: ../../data/
[Aug-06-2025_09-58-07] - dataset: ml-1m
[Aug-06-2025_09-58-07] - train_data: ml-1m.txt
[Aug-06-2025_09-58-07] - log_path: ../log
[Aug-06-2025_09-58-07] - num_layers: 2
[Aug-06-2025_09-58-07] - num_heads: 1
[Aug-06-2025_09-58-07] - inner_size: 256
[Aug-06-2025_09-58-07] - max_seq_len: 200
[Aug-06-2025_09-58-07] - upload_mode: full
[Aug-06-2025_09-58-07] - skip_test_eval: True
[Aug-06-2025_09-58-07] - eval_freq: 1
[Aug-06-2025_09-58-07] - c: 9
[Aug-06-2025_09-58-07] - alpha: 0.3
[Aug-06-2025_09-58-07] - 训练数据: ../../data/ml-1m/ml-1m.txt
[Aug-06-2025_09-58-07] - 最大序列长度: 200
[Aug-06-2025_09-58-07] - 批次大小: 128
[Aug-06-2025_09-58-11] - 参数上传模式: full
[Aug-06-2025_09-58-11] - 隐私参数（本地更新）: []
[Aug-06-2025_09-58-11] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'encoder.blocks.0.layer.filter_layer.sqrt_beta', 'encoder.blocks.0.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.0.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.0.layer.attention_layer.query.weight', 'encoder.blocks.0.layer.attention_layer.query.bias', 'encoder.blocks.0.layer.attention_layer.key.weight', 'encoder.blocks.0.layer.attention_layer.key.bias', 'encoder.blocks.0.layer.attention_layer.value.weight', 'encoder.blocks.0.layer.attention_layer.value.bias', 'encoder.blocks.0.layer.attention_layer.dense.weight', 'encoder.blocks.0.layer.attention_layer.dense.bias', 'encoder.blocks.0.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.0.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.0.feed_forward.dense_1.weight', 'encoder.blocks.0.feed_forward.dense_1.bias', 'encoder.blocks.0.feed_forward.dense_2.weight', 'encoder.blocks.0.feed_forward.dense_2.bias', 'encoder.blocks.0.feed_forward.LayerNorm.weight', 'encoder.blocks.0.feed_forward.LayerNorm.bias', 'encoder.blocks.1.layer.filter_layer.sqrt_beta', 'encoder.blocks.1.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.1.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.1.layer.attention_layer.query.weight', 'encoder.blocks.1.layer.attention_layer.query.bias', 'encoder.blocks.1.layer.attention_layer.key.weight', 'encoder.blocks.1.layer.attention_layer.key.bias', 'encoder.blocks.1.layer.attention_layer.value.weight', 'encoder.blocks.1.layer.attention_layer.value.bias', 'encoder.blocks.1.layer.attention_layer.dense.weight', 'encoder.blocks.1.layer.attention_layer.dense.bias', 'encoder.blocks.1.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.1.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.1.feed_forward.dense_1.weight', 'encoder.blocks.1.feed_forward.dense_1.bias', 'encoder.blocks.1.feed_forward.dense_2.weight', 'encoder.blocks.1.feed_forward.dense_2.bias', 'encoder.blocks.1.feed_forward.LayerNorm.weight', 'encoder.blocks.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-06-2025_09-58-11] - 用户数量: 6040
[Aug-06-2025_09-58-11] - 物品数量: 3416
[Aug-06-2025_10-02-23] - epoch:1, time: 252.011409(s), valid (NDCG@10: 0.2403, HR@10: 0.4373), test: SKIPPED, all_time: 252.011409(s)
[Aug-06-2025_10-02-23] - 新的最佳性能: valid NDCG@10=0.2403, valid HR@10=0.4373
[Aug-06-2025_10-06-39] - epoch:2, time: 255.959515(s), valid (NDCG@10: 0.2407, HR@10: 0.4344), test: SKIPPED, all_time: 507.970923(s)
[Aug-06-2025_10-09-50] - epoch:3, time: 190.819741(s), valid (NDCG@10: 0.2933, HR@10: 0.5245), test: SKIPPED, all_time: 698.790664(s)
[Aug-06-2025_10-09-50] - 新的最佳性能: valid NDCG@10=0.2933, valid HR@10=0.5245
[Aug-06-2025_10-13-24] - epoch:4, time: 214.230973(s), valid (NDCG@10: 0.3492, HR@10: 0.6008), test: SKIPPED, all_time: 913.021637(s)
[Aug-06-2025_10-13-24] - 新的最佳性能: valid NDCG@10=0.3492, valid HR@10=0.6008
[Aug-06-2025_10-17-09] - epoch:5, time: 224.697576(s), valid (NDCG@10: 0.3754, HR@10: 0.6318), test: SKIPPED, all_time: 1137.719213(s)
[Aug-06-2025_10-17-09] - 新的最佳性能: valid NDCG@10=0.3754, valid HR@10=0.6318
[Aug-06-2025_10-20-50] - epoch:6, time: 221.614921(s), valid (NDCG@10: 0.4144, HR@10: 0.6780), test: SKIPPED, all_time: 1359.334134(s)
[Aug-06-2025_10-20-50] - 新的最佳性能: valid NDCG@10=0.4144, valid HR@10=0.6780
[Aug-06-2025_10-24-23] - epoch:7, time: 212.712074(s), valid (NDCG@10: 0.4442, HR@10: 0.7070), test: SKIPPED, all_time: 1572.046208(s)
[Aug-06-2025_10-24-23] - 新的最佳性能: valid NDCG@10=0.4442, valid HR@10=0.7070
[Aug-06-2025_10-27-49] - epoch:8, time: 205.652304(s), valid (NDCG@10: 0.4611, HR@10: 0.7339), test: SKIPPED, all_time: 1777.698512(s)
[Aug-06-2025_10-27-49] - 新的最佳性能: valid NDCG@10=0.4611, valid HR@10=0.7339
[Aug-06-2025_10-31-54] - epoch:9, time: 245.570496(s), valid (NDCG@10: 0.4771, HR@10: 0.7447), test: SKIPPED, all_time: 2023.269008(s)
[Aug-06-2025_10-31-54] - 新的最佳性能: valid NDCG@10=0.4771, valid HR@10=0.7447
[Aug-06-2025_10-35-29] - epoch:10, time: 215.165582(s), valid (NDCG@10: 0.4905, HR@10: 0.7545), test: SKIPPED, all_time: 2238.434590(s)
[Aug-06-2025_10-35-29] - 新的最佳性能: valid NDCG@10=0.4905, valid HR@10=0.7545
[Aug-06-2025_10-39-12] - epoch:11, time: 222.971769(s), valid (NDCG@10: 0.4993, HR@10: 0.7568), test: SKIPPED, all_time: 2461.406359(s)
[Aug-06-2025_10-39-12] - 新的最佳性能: valid NDCG@10=0.4993, valid HR@10=0.7568
[Aug-06-2025_10-42-34] - epoch:12, time: 201.232735(s), valid (NDCG@10: 0.5043, HR@10: 0.7689), test: SKIPPED, all_time: 2662.639094(s)
[Aug-06-2025_10-42-34] - 新的最佳性能: valid NDCG@10=0.5043, valid HR@10=0.7689
[Aug-06-2025_10-46-12] - epoch:13, time: 218.928708(s), valid (NDCG@10: 0.5145, HR@10: 0.7682), test: SKIPPED, all_time: 2881.567802(s)
[Aug-06-2025_10-49-20] - epoch:14, time: 187.497283(s), valid (NDCG@10: 0.5193, HR@10: 0.7709), test: SKIPPED, all_time: 3069.065085(s)
[Aug-06-2025_10-49-20] - 新的最佳性能: valid NDCG@10=0.5193, valid HR@10=0.7709
[Aug-06-2025_10-53-47] - epoch:15, time: 267.140153(s), valid (NDCG@10: 0.5248, HR@10: 0.7720), test: SKIPPED, all_time: 3336.205238(s)
[Aug-06-2025_10-53-47] - 新的最佳性能: valid NDCG@10=0.5248, valid HR@10=0.7720
[Aug-06-2025_10-58-03] - epoch:16, time: 256.160020(s), valid (NDCG@10: 0.5242, HR@10: 0.7664), test: SKIPPED, all_time: 3592.365259(s)
[Aug-06-2025_11-02-10] - epoch:17, time: 246.730530(s), valid (NDCG@10: 0.5278, HR@10: 0.7747), test: SKIPPED, all_time: 3839.095789(s)
[Aug-06-2025_11-02-10] - 新的最佳性能: valid NDCG@10=0.5278, valid HR@10=0.7747
[Aug-06-2025_11-05-47] - epoch:18, time: 216.534862(s), valid (NDCG@10: 0.5276, HR@10: 0.7709), test: SKIPPED, all_time: 4055.630651(s)
[Aug-06-2025_11-09-28] - epoch:19, time: 221.475887(s), valid (NDCG@10: 0.5358, HR@10: 0.7740), test: SKIPPED, all_time: 4277.106538(s)
[Aug-06-2025_11-13-13] - epoch:20, time: 225.238307(s), valid (NDCG@10: 0.5371, HR@10: 0.7753), test: SKIPPED, all_time: 4502.344845(s)
[Aug-06-2025_11-13-13] - 新的最佳性能: valid NDCG@10=0.5371, valid HR@10=0.7753
[Aug-06-2025_11-16-41] - epoch:21, time: 207.462910(s), valid (NDCG@10: 0.5424, HR@10: 0.7776), test: SKIPPED, all_time: 4709.807755(s)
[Aug-06-2025_11-16-41] - 新的最佳性能: valid NDCG@10=0.5424, valid HR@10=0.7776
[Aug-06-2025_11-20-18] - epoch:22, time: 217.280495(s), valid (NDCG@10: 0.5416, HR@10: 0.7770), test: SKIPPED, all_time: 4927.088251(s)
[Aug-06-2025_11-25-07] - epoch:23, time: 289.383417(s), valid (NDCG@10: 0.5483, HR@10: 0.7760), test: SKIPPED, all_time: 5216.471668(s)
[Aug-06-2025_11-28-29] - epoch:24, time: 201.322028(s), valid (NDCG@10: 0.5474, HR@10: 0.7813), test: SKIPPED, all_time: 5417.793695(s)
[Aug-06-2025_11-28-29] - 新的最佳性能: valid NDCG@10=0.5483, valid HR@10=0.7813
[Aug-06-2025_11-31-20] - epoch:25, time: 171.253706(s), valid (NDCG@10: 0.5506, HR@10: 0.7800), test: SKIPPED, all_time: 5589.047402(s)
[Aug-06-2025_11-34-21] - epoch:26, time: 181.323674(s), valid (NDCG@10: 0.5485, HR@10: 0.7816), test: SKIPPED, all_time: 5770.371076(s)
[Aug-06-2025_11-34-21] - 新的最佳性能: valid NDCG@10=0.5506, valid HR@10=0.7816
[Aug-06-2025_11-37-22] - epoch:27, time: 180.788074(s), valid (NDCG@10: 0.5495, HR@10: 0.7806), test: SKIPPED, all_time: 5951.159150(s)
[Aug-06-2025_11-40-35] - epoch:28, time: 192.429716(s), valid (NDCG@10: 0.5482, HR@10: 0.7821), test: SKIPPED, all_time: 6143.588866(s)
[Aug-06-2025_11-40-35] - 新的最佳性能: valid NDCG@10=0.5506, valid HR@10=0.7821
[Aug-06-2025_11-44-23] - epoch:29, time: 228.861813(s), valid (NDCG@10: 0.5519, HR@10: 0.7859), test: SKIPPED, all_time: 6372.450680(s)
[Aug-06-2025_11-44-23] - 新的最佳性能: valid NDCG@10=0.5519, valid HR@10=0.7859
[Aug-06-2025_11-48-54] - epoch:30, time: 270.923982(s), valid (NDCG@10: 0.5501, HR@10: 0.7861), test: SKIPPED, all_time: 6643.374661(s)
[Aug-06-2025_11-48-54] - 新的最佳性能: valid NDCG@10=0.5519, valid HR@10=0.7861
[Aug-06-2025_11-52-46] - epoch:31, time: 231.413836(s), valid (NDCG@10: 0.5566, HR@10: 0.7879), test: SKIPPED, all_time: 6874.788498(s)
[Aug-06-2025_11-52-46] - 新的最佳性能: valid NDCG@10=0.5566, valid HR@10=0.7879
[Aug-06-2025_11-56-23] - epoch:32, time: 217.122218(s), valid (NDCG@10: 0.5557, HR@10: 0.7836), test: SKIPPED, all_time: 7091.910716(s)
[Aug-06-2025_11-59-48] - epoch:33, time: 205.540682(s), valid (NDCG@10: 0.5523, HR@10: 0.7838), test: SKIPPED, all_time: 7297.451398(s)
[Aug-06-2025_12-03-24] - epoch:34, time: 216.074235(s), valid (NDCG@10: 0.5549, HR@10: 0.7854), test: SKIPPED, all_time: 7513.525633(s)
[Aug-06-2025_12-06-40] - epoch:35, time: 195.416985(s), valid (NDCG@10: 0.5517, HR@10: 0.7813), test: SKIPPED, all_time: 7708.942618(s)
[Aug-06-2025_12-09-49] - epoch:36, time: 189.616037(s), valid (NDCG@10: 0.5533, HR@10: 0.7826), test: SKIPPED, all_time: 7898.558655(s)
[Aug-06-2025_12-13-23] - epoch:37, time: 213.951556(s), valid (NDCG@10: 0.5510, HR@10: 0.7826), test: SKIPPED, all_time: 8112.510211(s)
[Aug-06-2025_12-17-02] - epoch:38, time: 218.694427(s), valid (NDCG@10: 0.5551, HR@10: 0.7839), test: SKIPPED, all_time: 8331.204638(s)
[Aug-06-2025_12-20-34] - epoch:39, time: 212.102909(s), valid (NDCG@10: 0.5564, HR@10: 0.7831), test: SKIPPED, all_time: 8543.307546(s)
[Aug-06-2025_12-24-07] - epoch:40, time: 212.823339(s), valid (NDCG@10: 0.5542, HR@10: 0.7856), test: SKIPPED, all_time: 8756.130885(s)
[Aug-06-2025_12-27-11] - epoch:41, time: 184.271719(s), valid (NDCG@10: 0.5533, HR@10: 0.7806), test: SKIPPED, all_time: 8940.402604(s)
[Aug-06-2025_12-30-07] - epoch:42, time: 175.236626(s), valid (NDCG@10: 0.5511, HR@10: 0.7829), test: SKIPPED, all_time: 9115.639230(s)
[Aug-06-2025_12-33-04] - epoch:43, time: 177.367271(s), valid (NDCG@10: 0.5505, HR@10: 0.7795), test: SKIPPED, all_time: 9293.006502(s)
[Aug-06-2025_12-35-58] - epoch:44, time: 174.232753(s), valid (NDCG@10: 0.5486, HR@10: 0.7785), test: SKIPPED, all_time: 9467.239254(s)
[Aug-06-2025_12-38-51] - epoch:45, time: 172.510732(s), valid (NDCG@10: 0.5471, HR@10: 0.7753), test: SKIPPED, all_time: 9639.749987(s)
[Aug-06-2025_12-41-49] - epoch:46, time: 178.498646(s), valid (NDCG@10: 0.5467, HR@10: 0.7791), test: SKIPPED, all_time: 9818.248633(s)
[Aug-06-2025_12-44-44] - epoch:47, time: 174.617375(s), valid (NDCG@10: 0.5443, HR@10: 0.7733), test: SKIPPED, all_time: 9992.866008(s)
[Aug-06-2025_12-47-37] - epoch:48, time: 173.056768(s), valid (NDCG@10: 0.5481, HR@10: 0.7748), test: SKIPPED, all_time: 10165.922776(s)
[Aug-06-2025_12-50-32] - epoch:49, time: 175.030860(s), valid (NDCG@10: 0.5430, HR@10: 0.7719), test: SKIPPED, all_time: 10340.953636(s)
[Aug-06-2025_12-53-28] - epoch:50, time: 175.907005(s), valid (NDCG@10: 0.5412, HR@10: 0.7699), test: SKIPPED, all_time: 10516.860641(s)
[Aug-06-2025_12-56-23] - epoch:51, time: 174.809703(s), valid (NDCG@10: 0.5454, HR@10: 0.7768), test: SKIPPED, all_time: 10691.670344(s)
[Aug-06-2025_12-59-16] - epoch:52, time: 173.405108(s), valid (NDCG@10: 0.5452, HR@10: 0.7715), test: SKIPPED, all_time: 10865.075453(s)
[Aug-06-2025_13-02-14] - epoch:53, time: 177.604244(s), valid (NDCG@10: 0.5386, HR@10: 0.7682), test: SKIPPED, all_time: 11042.679697(s)
[Aug-06-2025_13-05-07] - epoch:54, time: 173.834740(s), valid (NDCG@10: 0.5408, HR@10: 0.7695), test: SKIPPED, all_time: 11216.514437(s)
[Aug-06-2025_13-08-02] - epoch:55, time: 174.208914(s), valid (NDCG@10: 0.5420, HR@10: 0.7674), test: SKIPPED, all_time: 11390.723351(s)
[Aug-06-2025_13-10-59] - epoch:56, time: 177.738497(s), valid (NDCG@10: 0.5439, HR@10: 0.7679), test: SKIPPED, all_time: 11568.461848(s)
[Aug-06-2025_13-13-54] - epoch:57, time: 174.648954(s), valid (NDCG@10: 0.5371, HR@10: 0.7644), test: SKIPPED, all_time: 11743.110803(s)
[Aug-06-2025_13-16-48] - epoch:58, time: 173.500241(s), valid (NDCG@10: 0.5377, HR@10: 0.7671), test: SKIPPED, all_time: 11916.611044(s)
[Aug-06-2025_13-19-40] - epoch:59, time: 172.586746(s), valid (NDCG@10: 0.5358, HR@10: 0.7659), test: SKIPPED, all_time: 12089.197790(s)
[Aug-06-2025_13-22-37] - epoch:60, time: 176.525337(s), valid (NDCG@10: 0.5329, HR@10: 0.7589), test: SKIPPED, all_time: 12265.723127(s)
[Aug-06-2025_13-25-31] - 早停触发！NDCG在30轮内没有改善。
[Aug-06-2025_13-25-31] - epoch:61, time: 174.557966(s), valid (NDCG@10: 0.5323, HR@10: 0.7621), test: SKIPPED, all_time: 12440.281093(s)
[Aug-06-2025_13-25-31] - [联邦训练] 最佳结果: valid NDCG@10=0.5566, HR@10=0.7879 (测试集评估已跳过)
