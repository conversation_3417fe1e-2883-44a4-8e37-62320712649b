{'early_stop_enabled': True, 'model': 'BSARec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'ml-1m', 'train_data': 'ml-1m.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': False, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False, 'c': 9, 'alpha': 0.3}
[Aug-04-2025_10-30-28] - 开始训练，配置参数如下：
[Aug-04-2025_10-30-28] - early_stop_enabled: True
[Aug-04-2025_10-30-28] - model: BSARec
[Aug-04-2025_10-30-28] - lr: 0.001
[Aug-04-2025_10-30-28] - batch_size: 128
[Aug-04-2025_10-30-28] - neg_num: 99
[Aug-04-2025_10-30-28] - l2_reg: 0
[Aug-04-2025_10-30-28] - l2_emb: 0.0
[Aug-04-2025_10-30-28] - hidden_size: 50
[Aug-04-2025_10-30-28] - dropout: 0.2
[Aug-04-2025_10-30-28] - epochs: 1000000
[Aug-04-2025_10-30-28] - early_stop: 50
[Aug-04-2025_10-30-28] - datapath: ../../data/
[Aug-04-2025_10-30-28] - dataset: ml-1m
[Aug-04-2025_10-30-28] - train_data: ml-1m.txt
[Aug-04-2025_10-30-28] - log_path: ../log
[Aug-04-2025_10-30-28] - num_layers: 2
[Aug-04-2025_10-30-28] - num_heads: 1
[Aug-04-2025_10-30-28] - inner_size: 256
[Aug-04-2025_10-30-28] - max_seq_len: 200
[Aug-04-2025_10-30-28] - upload_mode: full
[Aug-04-2025_10-30-28] - skip_test_eval: False
[Aug-04-2025_10-30-28] - eval_freq: 1
[Aug-04-2025_10-30-28] - use_dynamic_sampling: False
[Aug-04-2025_10-30-28] - norm_first: False
[Aug-04-2025_10-30-28] - c: 9
[Aug-04-2025_10-30-28] - alpha: 0.3
[Aug-04-2025_10-30-28] - 训练数据: ../../data/ml-1m/ml-1m.txt
[Aug-04-2025_10-30-28] - 最大序列长度: 200
[Aug-04-2025_10-30-28] - 批次大小: 128
[Aug-04-2025_10-30-31] - 参数上传模式: full
[Aug-04-2025_10-30-31] - 隐私参数（本地更新）: []
[Aug-04-2025_10-30-31] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'encoder.blocks.0.layer.filter_layer.sqrt_beta', 'encoder.blocks.0.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.0.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.0.layer.attention_layer.query.weight', 'encoder.blocks.0.layer.attention_layer.query.bias', 'encoder.blocks.0.layer.attention_layer.key.weight', 'encoder.blocks.0.layer.attention_layer.key.bias', 'encoder.blocks.0.layer.attention_layer.value.weight', 'encoder.blocks.0.layer.attention_layer.value.bias', 'encoder.blocks.0.layer.attention_layer.dense.weight', 'encoder.blocks.0.layer.attention_layer.dense.bias', 'encoder.blocks.0.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.0.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.0.feed_forward.dense_1.weight', 'encoder.blocks.0.feed_forward.dense_1.bias', 'encoder.blocks.0.feed_forward.dense_2.weight', 'encoder.blocks.0.feed_forward.dense_2.bias', 'encoder.blocks.0.feed_forward.LayerNorm.weight', 'encoder.blocks.0.feed_forward.LayerNorm.bias', 'encoder.blocks.1.layer.filter_layer.sqrt_beta', 'encoder.blocks.1.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.1.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.1.layer.attention_layer.query.weight', 'encoder.blocks.1.layer.attention_layer.query.bias', 'encoder.blocks.1.layer.attention_layer.key.weight', 'encoder.blocks.1.layer.attention_layer.key.bias', 'encoder.blocks.1.layer.attention_layer.value.weight', 'encoder.blocks.1.layer.attention_layer.value.bias', 'encoder.blocks.1.layer.attention_layer.dense.weight', 'encoder.blocks.1.layer.attention_layer.dense.bias', 'encoder.blocks.1.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.1.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.1.feed_forward.dense_1.weight', 'encoder.blocks.1.feed_forward.dense_1.bias', 'encoder.blocks.1.feed_forward.dense_2.weight', 'encoder.blocks.1.feed_forward.dense_2.bias', 'encoder.blocks.1.feed_forward.LayerNorm.weight', 'encoder.blocks.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-04-2025_10-30-31] - 动态负采样: False
[Aug-04-2025_10-30-31] - 用户数量: 6040
[Aug-04-2025_10-30-31] - 物品数量: 3416
[Aug-04-2025_10-33-26] - epoch:1, time: 99.882720(s), valid (NDCG@10: 0.2414, HR@10: 0.4414), test (NDCG@10: 0.2386, HR@10: 0.4387) all_time: 99.882720(s)
[Aug-04-2025_10-33-26] - 新的最佳性能: valid NDCG@10=0.2414, test NDCG@10=0.2386
[Aug-04-2025_10-36-23] - epoch:2, time: 101.752379(s), valid (NDCG@10: 0.2422, HR@10: 0.4389), test (NDCG@10: 0.2450, HR@10: 0.4419) all_time: 201.635099(s)
[Aug-04-2025_10-36-23] - 新的最佳性能: valid NDCG@10=0.2422, test NDCG@10=0.2450
[Aug-04-2025_10-39-27] - epoch:3, time: 98.155484(s), valid (NDCG@10: 0.2714, HR@10: 0.4906), test (NDCG@10: 0.2647, HR@10: 0.4785) all_time: 299.790583(s)
[Aug-04-2025_10-39-27] - 新的最佳性能: valid NDCG@10=0.2714, test NDCG@10=0.2647
[Aug-04-2025_10-42-34] - epoch:4, time: 104.816800(s), valid (NDCG@10: 0.3330, HR@10: 0.5737), test (NDCG@10: 0.3218, HR@10: 0.5500) all_time: 404.607383(s)
[Aug-04-2025_10-42-34] - 新的最佳性能: valid NDCG@10=0.3330, test NDCG@10=0.3218
[Aug-04-2025_10-45-37] - epoch:5, time: 105.104366(s), valid (NDCG@10: 0.3756, HR@10: 0.6272), test (NDCG@10: 0.3613, HR@10: 0.6098) all_time: 509.711749(s)
[Aug-04-2025_10-45-37] - 新的最佳性能: valid NDCG@10=0.3756, test NDCG@10=0.3613
[Aug-04-2025_10-48-52] - epoch:6, time: 105.129054(s), valid (NDCG@10: 0.4204, HR@10: 0.6828), test (NDCG@10: 0.4039, HR@10: 0.6627) all_time: 614.840803(s)
[Aug-04-2025_10-48-52] - 新的最佳性能: valid NDCG@10=0.4204, test NDCG@10=0.4039
[Aug-04-2025_10-52-00] - epoch:7, time: 105.662930(s), valid (NDCG@10: 0.4507, HR@10: 0.7189), test (NDCG@10: 0.4381, HR@10: 0.6964) all_time: 720.503733(s)
[Aug-04-2025_10-52-00] - 新的最佳性能: valid NDCG@10=0.4507, test NDCG@10=0.4381
[Aug-04-2025_10-54-58] - epoch:8, time: 108.502144(s), valid (NDCG@10: 0.4695, HR@10: 0.7369), test (NDCG@10: 0.4522, HR@10: 0.7109) all_time: 829.005877(s)
[Aug-04-2025_10-54-58] - 新的最佳性能: valid NDCG@10=0.4695, test NDCG@10=0.4522
[Aug-04-2025_10-57-39] - epoch:9, time: 94.317329(s), valid (NDCG@10: 0.4872, HR@10: 0.7483), test (NDCG@10: 0.4675, HR@10: 0.7303) all_time: 923.323207(s)
[Aug-04-2025_10-57-39] - 新的最佳性能: valid NDCG@10=0.4872, test NDCG@10=0.4675
[Aug-04-2025_11-00-16] - epoch:10, time: 94.362510(s), valid (NDCG@10: 0.4971, HR@10: 0.7568), test (NDCG@10: 0.4824, HR@10: 0.7396) all_time: 1017.685717(s)
[Aug-04-2025_11-00-16] - 新的最佳性能: valid NDCG@10=0.4971, test NDCG@10=0.4824
[Aug-04-2025_11-02-47] - epoch:11, taime: 88.115488(s), valid (NDCG@10: 0.5100, HR@10: 0.7647), test (NDCG@10: 0.4892, HR@10: 0.7462) all_time: 1105.801205(s)
[Aug-04-2025_11-02-47] - 新的最佳性能: valid NDCG@10=0.5100, test NDCG@10=0.4892
[Aug-04-2025_11-05-16] - epoch:12, time: 86.644315(s), valid (NDCG@10: 0.5151, HR@10: 0.7680), test (NDCG@10: 0.4948, HR@10: 0.7513) all_time: 1192.445520(s)
[Aug-04-2025_11-05-16] - 新的最佳性能: valid NDCG@10=0.5151, test NDCG@10=0.4948
[Aug-04-2025_11-07-46] - epoch:13, time: 86.428224(s), valid (NDCG@10: 0.5180, HR@10: 0.7709), test (NDCG@10: 0.4966, HR@10: 0.7507) all_time: 1278.873744(s)
[Aug-04-2025_11-07-46] - 新的最佳性能: valid NDCG@10=0.5180, test NDCG@10=0.4966
[Aug-04-2025_11-10-16] - epoch:14, time: 86.923233(s), valid (NDCG@10: 0.5219, HR@10: 0.7709), test (NDCG@10: 0.5005, HR@10: 0.7469) all_time: 1365.796976(s)
[Aug-04-2025_11-10-16] - 新的最佳性能: valid NDCG@10=0.5219, test NDCG@10=0.5005
[Aug-04-2025_11-12-46] - epoch:15, time: 86.557704(s), valid (NDCG@10: 0.5275, HR@10: 0.7722), test (NDCG@10: 0.5051, HR@10: 0.7490) all_time: 1452.354680(s)
[Aug-04-2025_11-12-46] - 新的最佳性能: valid NDCG@10=0.5275, test NDCG@10=0.5051
[Aug-04-2025_11-15-16] - epoch:16, time: 86.271991(s), valid (NDCG@10: 0.5305, HR@10: 0.7733), test (NDCG@10: 0.5013, HR@10: 0.7492) all_time: 1538.626672(s)
[Aug-04-2025_11-15-16] - 新的最佳性能: valid NDCG@10=0.5305, test NDCG@10=0.5051
[Aug-04-2025_11-17-46] - epoch:17, time: 87.056623(s), valid (NDCG@10: 0.5322, HR@10: 0.7758), test (NDCG@10: 0.5045, HR@10: 0.7483) all_time: 1625.683295(s)
[Aug-04-2025_11-17-46] - 新的最佳性能: valid NDCG@10=0.5322, test NDCG@10=0.5051
[Aug-04-2025_11-20-17] - epoch:18, time: 87.377528(s), valid (NDCG@10: 0.5353, HR@10: 0.7737), test (NDCG@10: 0.5057, HR@10: 0.7470) all_time: 1713.060823(s)
[Aug-04-2025_11-20-17] - 新的最佳性能: valid NDCG@10=0.5353, test NDCG@10=0.5057
[Aug-04-2025_11-22-47] - epoch:19, time: 86.767379(s), valid (NDCG@10: 0.5372, HR@10: 0.7724), test (NDCG@10: 0.5102, HR@10: 0.7512) all_time: 1799.828202(s)
[Aug-04-2025_11-22-47] - 新的最佳性能: valid NDCG@10=0.5372, test NDCG@10=0.5102
[Aug-04-2025_11-25-18] - epoch:20, time: 86.810402(s), valid (NDCG@10: 0.5390, HR@10: 0.7755), test (NDCG@10: 0.5123, HR@10: 0.7545) all_time: 1886.638604(s)
[Aug-04-2025_11-25-18] - 新的最佳性能: valid NDCG@10=0.5390, test NDCG@10=0.5123
[Aug-04-2025_11-27-47] - epoch:21, time: 86.515008(s), valid (NDCG@10: 0.5413, HR@10: 0.7753), test (NDCG@10: 0.5126, HR@10: 0.7561) all_time: 1973.153612(s)
[Aug-04-2025_11-27-47] - 新的最佳性能: valid NDCG@10=0.5413, test NDCG@10=0.5126
[Aug-04-2025_11-30-16] - epoch:22, time: 86.195987(s), valid (NDCG@10: 0.5434, HR@10: 0.7768), test (NDCG@10: 0.5170, HR@10: 0.7584) all_time: 2059.349599(s)
[Aug-04-2025_11-30-16] - 新的最佳性能: valid NDCG@10=0.5434, test NDCG@10=0.5170
[Aug-04-2025_11-32-45] - epoch:23, time: 86.009063(s), valid (NDCG@10: 0.5464, HR@10: 0.7798), test (NDCG@10: 0.5184, HR@10: 0.7575) all_time: 2145.358662(s)
[Aug-04-2025_11-32-45] - 新的最佳性能: valid NDCG@10=0.5464, test NDCG@10=0.5184
[Aug-04-2025_11-35-15] - epoch:24, time: 86.501708(s), valid (NDCG@10: 0.5469, HR@10: 0.7757), test (NDCG@10: 0.5201, HR@10: 0.7550) all_time: 2231.860370(s)
[Aug-04-2025_11-35-15] - 新的最佳性能: valid NDCG@10=0.5469, test NDCG@10=0.5201
[Aug-04-2025_11-37-45] - epoch:25, time: 87.358506(s), valid (NDCG@10: 0.5491, HR@10: 0.7776), test (NDCG@10: 0.5194, HR@10: 0.7584) all_time: 2319.218876(s)
[Aug-04-2025_11-40-15] - epoch:26, time: 86.699437(s), valid (NDCG@10: 0.5471, HR@10: 0.7745), test (NDCG@10: 0.5241, HR@10: 0.7584) all_time: 2405.918313(s)
[Aug-04-2025_11-40-15] - 新的最佳性能: valid NDCG@10=0.5491, test NDCG@10=0.5241
[Aug-04-2025_11-42-43] - epoch:27, time: 85.907121(s), valid (NDCG@10: 0.5521, HR@10: 0.7772), test (NDCG@10: 0.5197, HR@10: 0.7541) all_time: 2491.825434(s)
[Aug-04-2025_11-45-12] - epoch:28, time: 85.997580(s), valid (NDCG@10: 0.5517, HR@10: 0.7752), test (NDCG@10: 0.5241, HR@10: 0.7565) all_time: 2577.823014(s)
[Aug-04-2025_11-45-12] - 新的最佳性能: valid NDCG@10=0.5521, test NDCG@10=0.5241
[Aug-04-2025_11-47-41] - epoch:29, time: 85.323611(s), valid (NDCG@10: 0.5494, HR@10: 0.7763), test (NDCG@10: 0.5226, HR@10: 0.7543) all_time: 2663.146625(s)
[Aug-04-2025_11-50-11] - epoch:30, time: 86.971225(s), valid (NDCG@10: 0.5525, HR@10: 0.7795), test (NDCG@10: 0.5231, HR@10: 0.7545) all_time: 2750.117850(s)
[Aug-04-2025_11-52-41] - epoch:31, time: 87.158966(s), valid (NDCG@10: 0.5464, HR@10: 0.7773), test (NDCG@10: 0.5249, HR@10: 0.7546) all_time: 2837.276816(s)
[Aug-04-2025_11-52-41] - 新的最佳性能: valid NDCG@10=0.5525, test NDCG@10=0.5249
[Aug-04-2025_11-55-10] - epoch:32, time: 86.292551(s), valid (NDCG@10: 0.5540, HR@10: 0.7772), test (NDCG@10: 0.5239, HR@10: 0.7540) all_time: 2923.569367(s)
[Aug-04-2025_11-57-40] - epoch:33, time: 86.629805(s), valid (NDCG@10: 0.5516, HR@10: 0.7775), test (NDCG@10: 0.5263, HR@10: 0.7505) all_time: 3010.199172(s)
[Aug-04-2025_11-57-40] - 新的最佳性能: valid NDCG@10=0.5540, test NDCG@10=0.5263
[Aug-04-2025_12-00-08] - epoch:34, time: 86.150116(s), valid (NDCG@10: 0.5500, HR@10: 0.7745), test (NDCG@10: 0.5205, HR@10: 0.7483) all_time: 3096.349288(s)
[Aug-04-2025_12-03-03] - epoch:35, time: 90.956694(s), valid (NDCG@10: 0.5518, HR@10: 0.7770), test (NDCG@10: 0.5212, HR@10: 0.7522) all_time: 3187.305981(s)
[Aug-04-2025_12-05-35] - epoch:36, time: 88.889655(s), valid (NDCG@10: 0.5542, HR@10: 0.7776), test (NDCG@10: 0.5221, HR@10: 0.7485) all_time: 3276.195637(s)
[Aug-04-2025_12-08-28] - epoch:37, time: 91.189243(s), valid (NDCG@10: 0.5526, HR@10: 0.7762), test (NDCG@10: 0.5268, HR@10: 0.7568) all_time: 3367.384879(s)
[Aug-04-2025_12-08-28] - 新的最佳性能: valid NDCG@10=0.5542, test NDCG@10=0.5268
[Aug-04-2025_12-10-59] - epoch:38, time: 87.291276(s), valid (NDCG@10: 0.5508, HR@10: 0.7780), test (NDCG@10: 0.5168, HR@10: 0.7507) all_time: 3454.676155(s)
[Aug-04-2025_12-13-29] - epoch:39, time: 86.714292(s), valid (NDCG@10: 0.5488, HR@10: 0.7745), test (NDCG@10: 0.5225, HR@10: 0.7470) all_time: 3541.390447(s)
[Aug-04-2025_12-15-59] - epoch:40, time: 86.492362(s), valid (NDCG@10: 0.5510, HR@10: 0.7740), test (NDCG@10: 0.5216, HR@10: 0.7526) all_time: 3627.882809(s)
[Aug-04-2025_12-18-52] - epoch:41, time: 93.393215(s), valid (NDCG@10: 0.5511, HR@10: 0.7760), test (NDCG@10: 0.5230, HR@10: 0.7512) all_time: 3721.276025(s)
[Aug-04-2025_12-21-22] - epoch:42, time: 86.726658(s), valid (NDCG@10: 0.5469, HR@10: 0.7697), test (NDCG@10: 0.5172, HR@10: 0.7460) all_time: 3808.002682(s)
[Aug-04-2025_12-23-52] - epoch:43, time: 86.753384(s), valid (NDCG@10: 0.5504, HR@10: 0.7762), test (NDCG@10: 0.5233, HR@10: 0.7517) all_time: 3894.756067(s)
[Aug-04-2025_12-26-44] - epoch:44, time: 86.868996(s), valid (NDCG@10: 0.5508, HR@10: 0.7775), test (NDCG@10: 0.5201, HR@10: 0.7490) all_time: 3981.625063(s)
[Aug-04-2025_12-29-44] - epoch:45, time: 98.409552(s), valid (NDCG@10: 0.5479, HR@10: 0.7752), test (NDCG@10: 0.5188, HR@10: 0.7507) all_time: 4080.034614(s)
[Aug-04-2025_12-32-41] - epoch:46, time: 101.107663(s), valid (NDCG@10: 0.5504, HR@10: 0.7735), test (NDCG@10: 0.5202, HR@10: 0.7470) all_time: 4181.142278(s)
[Aug-04-2025_12-35-39] - epoch:47, time: 97.523353(s), valid (NDCG@10: 0.5425, HR@10: 0.7692), test (NDCG@10: 0.5174, HR@10: 0.7464) all_time: 4278.665631(s)
[Aug-04-2025_12-38-39] - epoch:48, time: 96.862398(s), valid (NDCG@10: 0.5500, HR@10: 0.7707), test (NDCG@10: 0.5203, HR@10: 0.7475) all_time: 4375.528029(s)
[Aug-04-2025_12-41-32] - epoch:49, time: 97.101843(s), valid (NDCG@10: 0.5455, HR@10: 0.7709), test (NDCG@10: 0.5174, HR@10: 0.7462) all_time: 4472.629872(s)
[Aug-04-2025_12-44-29] - epoch:50, time: 99.331247(s), valid (NDCG@10: 0.5445, HR@10: 0.7712), test (NDCG@10: 0.5186, HR@10: 0.7490) all_time: 4571.961119(s)
[Aug-04-2025_12-47-29] - epoch:51, time: 96.386909(s), valid (NDCG@10: 0.5450, HR@10: 0.7712), test (NDCG@10: 0.5162, HR@10: 0.7480) all_time: 4668.348028(s)
[Aug-04-2025_12-50-22] - epoch:52, time: 96.808210(s), valid (NDCG@10: 0.5439, HR@10: 0.7694), test (NDCG@10: 0.5171, HR@10: 0.7478) all_time: 4765.156238(s)
[Aug-04-2025_12-53-19] - epoch:53, time: 100.658314(s), valid (NDCG@10: 0.5425, HR@10: 0.7719), test (NDCG@10: 0.5142, HR@10: 0.7478) all_time: 4865.814552(s)
[Aug-04-2025_12-56-20] - epoch:54, time: 96.955315(s), valid (NDCG@10: 0.5419, HR@10: 0.7664), test (NDCG@10: 0.5136, HR@10: 0.7460) all_time: 4962.769867(s)
[Aug-04-2025_12-59-14] - epoch:55, time: 96.101729(s), valid (NDCG@10: 0.5406, HR@10: 0.7662), test (NDCG@10: 0.5128, HR@10: 0.7469) all_time: 5058.871596(s)
[Aug-04-2025_13-02-09] - epoch:56, time: 99.061447(s), valid (NDCG@10: 0.5433, HR@10: 0.7662), test (NDCG@10: 0.5171, HR@10: 0.7502) all_time: 5157.933043(s)
[Aug-04-2025_13-05-07] - epoch:57, time: 97.508142(s), valid (NDCG@10: 0.5377, HR@10: 0.7646), test (NDCG@10: 0.5106, HR@10: 0.7467) all_time: 5255.441185(s)
[Aug-04-2025_13-08-04] - epoch:58, time: 95.830987(s), valid (NDCG@10: 0.5420, HR@10: 0.7659), test (NDCG@10: 0.5156, HR@10: 0.7442) all_time: 5351.272171(s)
[Aug-04-2025_13-10-58] - epoch:59, time: 97.374008(s), valid (NDCG@10: 0.5414, HR@10: 0.7632), test (NDCG@10: 0.5127, HR@10: 0.7434) all_time: 5448.646179(s)
[Aug-04-2025_13-13-59] - epoch:60, time: 100.434080(s), valid (NDCG@10: 0.5377, HR@10: 0.7644), test (NDCG@10: 0.5086, HR@10: 0.7425) all_time: 5549.080260(s)
[Aug-04-2025_13-17-01] - epoch:61, time: 97.999965(s), valid (NDCG@10: 0.5400, HR@10: 0.7649), test (NDCG@10: 0.5103, HR@10: 0.7450) all_time: 5647.080225(s)
[Aug-04-2025_13-19-57] - epoch:62, time: 98.241123(s), valid (NDCG@10: 0.5412, HR@10: 0.7636), test (NDCG@10: 0.5106, HR@10: 0.7430) all_time: 5745.321348(s)
[Aug-04-2025_13-22-54] - epoch:63, time: 101.456716(s), valid (NDCG@10: 0.5363, HR@10: 0.7621), test (NDCG@10: 0.5072, HR@10: 0.7377) all_time: 5846.778064(s)
