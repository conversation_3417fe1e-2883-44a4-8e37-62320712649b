0.5505,0.3294
{'early_stop_enabled': True, 'model': 'BSARec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000, 'early_stop': 30, 'datapath': '../../data/', 'dataset': 'LastFM', 'train_data': 'LastFM.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'c': 9, 'alpha': 0.7}
[Aug-06-2025_00-32-40] - 开始训练，配置参数如下：
[Aug-06-2025_00-32-40] - early_stop_enabled: True
[Aug-06-2025_00-32-40] - model: BSARec
[Aug-06-2025_00-32-40] - lr: 0.001
[Aug-06-2025_00-32-40] - batch_size: 128
[Aug-06-2025_00-32-40] - neg_num: 99
[Aug-06-2025_00-32-40] - l2_reg: 0
[Aug-06-2025_00-32-40] - l2_emb: 0.0
[Aug-06-2025_00-32-40] - hidden_size: 50
[Aug-06-2025_00-32-40] - dropout: 0.2
[Aug-06-2025_00-32-40] - epochs: 1000
[Aug-06-2025_00-32-40] - early_stop: 30
[Aug-06-2025_00-32-40] - datapath: ../../data/
[Aug-06-2025_00-32-40] - dataset: LastFM
[Aug-06-2025_00-32-40] - train_data: LastFM.txt
[Aug-06-2025_00-32-40] - log_path: ../log
[Aug-06-2025_00-32-40] - num_layers: 2
[Aug-06-2025_00-32-40] - num_heads: 1
[Aug-06-2025_00-32-40] - inner_size: 256
[Aug-06-2025_00-32-40] - max_seq_len: 200
[Aug-06-2025_00-32-40] - upload_mode: full
[Aug-06-2025_00-32-40] - skip_test_eval: True
[Aug-06-2025_00-32-40] - eval_freq: 1
[Aug-06-2025_00-32-40] - c: 9
[Aug-06-2025_00-32-40] - alpha: 0.7
[Aug-06-2025_00-32-40] - 训练数据: ../../data/LastFM/LastFM.txt
[Aug-06-2025_00-32-40] - 最大序列长度: 200
[Aug-06-2025_00-32-40] - 批次大小: 128
[Aug-06-2025_00-32-41] - 参数上传模式: full
[Aug-06-2025_00-32-41] - 隐私参数（本地更新）: []
[Aug-06-2025_00-32-41] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'encoder.blocks.0.layer.filter_layer.sqrt_beta', 'encoder.blocks.0.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.0.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.0.layer.attention_layer.query.weight', 'encoder.blocks.0.layer.attention_layer.query.bias', 'encoder.blocks.0.layer.attention_layer.key.weight', 'encoder.blocks.0.layer.attention_layer.key.bias', 'encoder.blocks.0.layer.attention_layer.value.weight', 'encoder.blocks.0.layer.attention_layer.value.bias', 'encoder.blocks.0.layer.attention_layer.dense.weight', 'encoder.blocks.0.layer.attention_layer.dense.bias', 'encoder.blocks.0.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.0.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.0.feed_forward.dense_1.weight', 'encoder.blocks.0.feed_forward.dense_1.bias', 'encoder.blocks.0.feed_forward.dense_2.weight', 'encoder.blocks.0.feed_forward.dense_2.bias', 'encoder.blocks.0.feed_forward.LayerNorm.weight', 'encoder.blocks.0.feed_forward.LayerNorm.bias', 'encoder.blocks.1.layer.filter_layer.sqrt_beta', 'encoder.blocks.1.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.1.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.1.layer.attention_layer.query.weight', 'encoder.blocks.1.layer.attention_layer.query.bias', 'encoder.blocks.1.layer.attention_layer.key.weight', 'encoder.blocks.1.layer.attention_layer.key.bias', 'encoder.blocks.1.layer.attention_layer.value.weight', 'encoder.blocks.1.layer.attention_layer.value.bias', 'encoder.blocks.1.layer.attention_layer.dense.weight', 'encoder.blocks.1.layer.attention_layer.dense.bias', 'encoder.blocks.1.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.1.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.1.feed_forward.dense_1.weight', 'encoder.blocks.1.feed_forward.dense_1.bias', 'encoder.blocks.1.feed_forward.dense_2.weight', 'encoder.blocks.1.feed_forward.dense_2.bias', 'encoder.blocks.1.feed_forward.LayerNorm.weight', 'encoder.blocks.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-06-2025_00-32-41] - 用户数量: 1090
[Aug-06-2025_00-32-41] - 物品数量: 3646
[Aug-06-2025_00-33-07] - epoch:1, time: 26.495301(s), valid (NDCG@10: 0.0845, HR@10: 0.1642), test: SKIPPED, all_time: 26.495301(s)
[Aug-06-2025_00-33-07] - 新的最佳性能: valid NDCG@10=0.0845, valid HR@10=0.1642
[Aug-06-2025_00-33-35] - epoch:2, time: 27.854368(s), valid (NDCG@10: 0.1500, HR@10: 0.2835), test: SKIPPED, all_time: 54.349669(s)
[Aug-06-2025_00-33-35] - 新的最佳性能: valid NDCG@10=0.1500, valid HR@10=0.2835
[Aug-06-2025_00-34-02] - epoch:3, time: 26.530548(s), valid (NDCG@10: 0.1684, HR@10: 0.3055), test: SKIPPED, all_time: 80.880217(s)
[Aug-06-2025_00-34-02] - 新的最佳性能: valid NDCG@10=0.1684, valid HR@10=0.3055
[Aug-06-2025_00-34-27] - epoch:4, time: 25.308835(s), valid (NDCG@10: 0.1646, HR@10: 0.2954), test: SKIPPED, all_time: 106.189052(s)
[Aug-06-2025_00-34-55] - epoch:5, time: 27.508326(s), valid (NDCG@10: 0.1711, HR@10: 0.3110), test: SKIPPED, all_time: 133.697378(s)
[Aug-06-2025_00-34-55] - 新的最佳性能: valid NDCG@10=0.1711, valid HR@10=0.3110
[Aug-06-2025_00-35-23] - epoch:6, time: 28.798605(s), valid (NDCG@10: 0.1741, HR@10: 0.3110), test: SKIPPED, all_time: 162.495983(s)
[Aug-06-2025_00-35-50] - epoch:7, time: 26.093201(s), valid (NDCG@10: 0.1708, HR@10: 0.3055), test: SKIPPED, all_time: 188.589184(s)
[Aug-06-2025_00-36-15] - epoch:8, time: 25.720913(s), valid (NDCG@10: 0.1681, HR@10: 0.3046), test: SKIPPED, all_time: 214.310098(s)
[Aug-06-2025_00-36-42] - epoch:9, time: 26.257997(s), valid (NDCG@10: 0.1773, HR@10: 0.3092), test: SKIPPED, all_time: 240.568095(s)
[Aug-06-2025_00-37-08] - epoch:10, time: 26.168444(s), valid (NDCG@10: 0.1826, HR@10: 0.3147), test: SKIPPED, all_time: 266.736539(s)
[Aug-06-2025_00-37-08] - 新的最佳性能: valid NDCG@10=0.1826, valid HR@10=0.3147
[Aug-06-2025_00-37-34] - epoch:11, time: 26.730635(s), valid (NDCG@10: 0.1849, HR@10: 0.3321), test: SKIPPED, all_time: 293.467174(s)
[Aug-06-2025_00-37-34] - 新的最佳性能: valid NDCG@10=0.1849, valid HR@10=0.3321
[Aug-06-2025_00-38-02] - epoch:12, time: 27.212736(s), valid (NDCG@10: 0.2001, HR@10: 0.3532), test: SKIPPED, all_time: 320.679910(s)
[Aug-06-2025_00-38-02] - 新的最佳性能: valid NDCG@10=0.2001, valid HR@10=0.3532
[Aug-06-2025_00-38-30] - epoch:13, time: 28.766835(s), valid (NDCG@10: 0.2059, HR@10: 0.3624), test: SKIPPED, all_time: 349.446745(s)
[Aug-06-2025_00-38-30] - 新的最佳性能: valid NDCG@10=0.2059, valid HR@10=0.3624
[Aug-06-2025_00-38-58] - epoch:14, time: 27.595421(s), valid (NDCG@10: 0.2194, HR@10: 0.3798), test: SKIPPED, all_time: 377.042165(s)
[Aug-06-2025_00-38-58] - 新的最佳性能: valid NDCG@10=0.2194, valid HR@10=0.3798
[Aug-06-2025_00-39-27] - epoch:15, time: 29.007319(s), valid (NDCG@10: 0.2284, HR@10: 0.3917), test: SKIPPED, all_time: 406.049484(s)
[Aug-06-2025_00-39-27] - 新的最佳性能: valid NDCG@10=0.2284, valid HR@10=0.3917
[Aug-06-2025_00-39-56] - epoch:16, time: 28.792091(s), valid (NDCG@10: 0.2341, HR@10: 0.4055), test: SKIPPED, all_time: 434.841575(s)
[Aug-06-2025_00-39-56] - 新的最佳性能: valid NDCG@10=0.2341, valid HR@10=0.4055
[Aug-06-2025_00-40-36] - epoch:17, time: 39.866390(s), valid (NDCG@10: 0.2451, HR@10: 0.4257), test: SKIPPED, all_time: 474.707965(s)
[Aug-06-2025_00-40-36] - 新的最佳性能: valid NDCG@10=0.2451, valid HR@10=0.4257
[Aug-06-2025_00-41-22] - epoch:18, time: 46.720266(s), valid (NDCG@10: 0.2553, HR@10: 0.4220), test: SKIPPED, all_time: 521.428231(s)
[Aug-06-2025_00-42-09] - epoch:19, time: 46.964145(s), valid (NDCG@10: 0.2558, HR@10: 0.4358), test: SKIPPED, all_time: 568.392376(s)
[Aug-06-2025_00-42-09] - 新的最佳性能: valid NDCG@10=0.2558, valid HR@10=0.4358
[Aug-06-2025_00-42-58] - epoch:20, time: 48.906281(s), valid (NDCG@10: 0.2650, HR@10: 0.4477), test: SKIPPED, all_time: 617.298657(s)
[Aug-06-2025_00-42-58] - 新的最佳性能: valid NDCG@10=0.2650, valid HR@10=0.4477
[Aug-06-2025_00-43-48] - epoch:21, time: 49.370652(s), valid (NDCG@10: 0.2669, HR@10: 0.4422), test: SKIPPED, all_time: 666.669309(s)
[Aug-06-2025_00-44-38] - epoch:22, time: 50.506083(s), valid (NDCG@10: 0.2620, HR@10: 0.4422), test: SKIPPED, all_time: 717.175392(s)
[Aug-06-2025_00-45-25] - epoch:23, time: 47.069050(s), valid (NDCG@10: 0.2711, HR@10: 0.4670), test: SKIPPED, all_time: 764.244442(s)
[Aug-06-2025_00-45-25] - 新的最佳性能: valid NDCG@10=0.2711, valid HR@10=0.4670
[Aug-06-2025_00-46-15] - epoch:24, time: 49.658058(s), valid (NDCG@10: 0.2804, HR@10: 0.4606), test: SKIPPED, all_time: 813.902499(s)
[Aug-06-2025_00-47-02] - epoch:25, time: 47.415400(s), valid (NDCG@10: 0.2734, HR@10: 0.4606), test: SKIPPED, all_time: 861.317899(s)
[Aug-06-2025_00-47-51] - epoch:26, time: 48.342960(s), valid (NDCG@10: 0.2836, HR@10: 0.4807), test: SKIPPED, all_time: 909.660859(s)
[Aug-06-2025_00-47-51] - 新的最佳性能: valid NDCG@10=0.2836, valid HR@10=0.4807
[Aug-06-2025_00-48-40] - epoch:27, time: 49.377331(s), valid (NDCG@10: 0.2868, HR@10: 0.4743), test: SKIPPED, all_time: 959.038190(s)
[Aug-06-2025_00-49-27] - epoch:28, time: 46.688708(s), valid (NDCG@10: 0.2901, HR@10: 0.4862), test: SKIPPED, all_time: 1005.726899(s)
[Aug-06-2025_00-49-27] - 新的最佳性能: valid NDCG@10=0.2901, valid HR@10=0.4862
[Aug-06-2025_00-50-14] - epoch:29, time: 46.820397(s), valid (NDCG@10: 0.2860, HR@10: 0.4771), test: SKIPPED, all_time: 1052.547295(s)
[Aug-06-2025_00-50-59] - epoch:30, time: 45.830441(s), valid (NDCG@10: 0.2878, HR@10: 0.4853), test: SKIPPED, all_time: 1098.377736(s)
[Aug-06-2025_00-51-46] - epoch:31, time: 46.584737(s), valid (NDCG@10: 0.2919, HR@10: 0.4908), test: SKIPPED, all_time: 1144.962472(s)
[Aug-06-2025_00-51-46] - 新的最佳性能: valid NDCG@10=0.2919, valid HR@10=0.4908
[Aug-06-2025_00-52-32] - epoch:32, time: 46.331402(s), valid (NDCG@10: 0.2995, HR@10: 0.4927), test: SKIPPED, all_time: 1191.293874(s)
[Aug-06-2025_00-52-32] - 新的最佳性能: valid NDCG@10=0.2995, valid HR@10=0.4927
[Aug-06-2025_00-53-19] - epoch:33, time: 46.487103(s), valid (NDCG@10: 0.2921, HR@10: 0.4972), test: SKIPPED, all_time: 1237.780977(s)
[Aug-06-2025_00-53-19] - 新的最佳性能: valid NDCG@10=0.2995, valid HR@10=0.4972
[Aug-06-2025_00-54-07] - epoch:34, time: 47.957849(s), valid (NDCG@10: 0.3005, HR@10: 0.4982), test: SKIPPED, all_time: 1285.738826(s)
[Aug-06-2025_00-54-07] - 新的最佳性能: valid NDCG@10=0.3005, valid HR@10=0.4982
[Aug-06-2025_00-54-53] - epoch:35, time: 45.828138(s), valid (NDCG@10: 0.2939, HR@10: 0.4936), test: SKIPPED, all_time: 1331.566965(s)
[Aug-06-2025_00-55-40] - epoch:36, time: 47.743855(s), valid (NDCG@10: 0.2996, HR@10: 0.5046), test: SKIPPED, all_time: 1379.310820(s)
[Aug-06-2025_00-55-40] - 新的最佳性能: valid NDCG@10=0.3005, valid HR@10=0.5046
[Aug-06-2025_00-56-25] - epoch:37, time: 44.965175(s), valid (NDCG@10: 0.3071, HR@10: 0.5092), test: SKIPPED, all_time: 1424.275995(s)
[Aug-06-2025_00-56-25] - 新的最佳性能: valid NDCG@10=0.3071, valid HR@10=0.5092
[Aug-06-2025_00-57-11] - epoch:38, time: 46.029815(s), valid (NDCG@10: 0.2998, HR@10: 0.5055), test: SKIPPED, all_time: 1470.305809(s)
[Aug-06-2025_00-57-58] - epoch:39, time: 46.985131(s), valid (NDCG@10: 0.3120, HR@10: 0.5119), test: SKIPPED, all_time: 1517.290941(s)
[Aug-06-2025_00-57-58] - 新的最佳性能: valid NDCG@10=0.3120, valid HR@10=0.5119
[Aug-06-2025_00-58-43] - epoch:40, time: 44.986821(s), valid (NDCG@10: 0.3047, HR@10: 0.5110), test: SKIPPED, all_time: 1562.277762(s)
[Aug-06-2025_00-59-28] - epoch:41, time: 44.509212(s), valid (NDCG@10: 0.3046, HR@10: 0.5110), test: SKIPPED, all_time: 1606.786974(s)
[Aug-06-2025_01-00-13] - epoch:42, time: 45.587456(s), valid (NDCG@10: 0.3124, HR@10: 0.5165), test: SKIPPED, all_time: 1652.374430(s)
[Aug-06-2025_01-00-13] - 新的最佳性能: valid NDCG@10=0.3124, valid HR@10=0.5165
[Aug-06-2025_01-01-00] - epoch:43, time: 47.101460(s), valid (NDCG@10: 0.3143, HR@10: 0.5312), test: SKIPPED, all_time: 1699.475890(s)
[Aug-06-2025_01-01-00] - 新的最佳性能: valid NDCG@10=0.3143, valid HR@10=0.5312
[Aug-06-2025_01-01-45] - epoch:44, time: 44.907182(s), valid (NDCG@10: 0.3180, HR@10: 0.5220), test: SKIPPED, all_time: 1744.383072(s)
[Aug-06-2025_01-02-33] - epoch:45, time: 47.182081(s), valid (NDCG@10: 0.3132, HR@10: 0.5321), test: SKIPPED, all_time: 1791.565154(s)
[Aug-06-2025_01-02-33] - 新的最佳性能: valid NDCG@10=0.3180, valid HR@10=0.5321
[Aug-06-2025_01-03-18] - epoch:46, time: 45.161204(s), valid (NDCG@10: 0.3057, HR@10: 0.5165), test: SKIPPED, all_time: 1836.726358(s)
[Aug-06-2025_01-03-50] - epoch:47, time: 32.358415(s), valid (NDCG@10: 0.3095, HR@10: 0.5275), test: SKIPPED, all_time: 1869.084773(s)
[Aug-06-2025_01-04-16] - epoch:48, time: 25.499519(s), valid (NDCG@10: 0.3126, HR@10: 0.5266), test: SKIPPED, all_time: 1894.584291(s)
[Aug-06-2025_01-04-40] - epoch:49, time: 24.795873(s), valid (NDCG@10: 0.3144, HR@10: 0.5257), test: SKIPPED, all_time: 1919.380164(s)
[Aug-06-2025_01-05-04] - epoch:50, time: 23.985792(s), valid (NDCG@10: 0.3118, HR@10: 0.5284), test: SKIPPED, all_time: 1943.365956(s)
[Aug-06-2025_01-05-28] - epoch:51, time: 23.734767(s), valid (NDCG@10: 0.3229, HR@10: 0.5468), test: SKIPPED, all_time: 1967.100723(s)
[Aug-06-2025_01-05-28] - 新的最佳性能: valid NDCG@10=0.3229, valid HR@10=0.5468
[Aug-06-2025_01-05-52] - epoch:52, time: 23.395807(s), valid (NDCG@10: 0.3172, HR@10: 0.5349), test: SKIPPED, all_time: 1990.496530(s)
[Aug-06-2025_01-06-15] - epoch:53, time: 23.158723(s), valid (NDCG@10: 0.3161, HR@10: 0.5394), test: SKIPPED, all_time: 2013.655253(s)
[Aug-06-2025_01-06-41] - epoch:54, time: 26.697294(s), valid (NDCG@10: 0.3196, HR@10: 0.5394), test: SKIPPED, all_time: 2040.352547(s)
[Aug-06-2025_01-07-09] - epoch:55, time: 27.811853(s), valid (NDCG@10: 0.3194, HR@10: 0.5275), test: SKIPPED, all_time: 2068.164401(s)
[Aug-06-2025_01-07-39] - epoch:56, time: 29.582480(s), valid (NDCG@10: 0.3226, HR@10: 0.5376), test: SKIPPED, all_time: 2097.746880(s)
[Aug-06-2025_01-08-08] - epoch:57, time: 29.404126(s), valid (NDCG@10: 0.3197, HR@10: 0.5413), test: SKIPPED, all_time: 2127.151007(s)
[Aug-06-2025_01-08-35] - epoch:58, time: 26.740977(s), valid (NDCG@10: 0.3229, HR@10: 0.5413), test: SKIPPED, all_time: 2153.891983(s)
[Aug-06-2025_01-09-00] - epoch:59, time: 24.914466(s), valid (NDCG@10: 0.3254, HR@10: 0.5367), test: SKIPPED, all_time: 2178.806449(s)
[Aug-06-2025_01-09-26] - epoch:60, time: 25.775979(s), valid (NDCG@10: 0.3241, HR@10: 0.5422), test: SKIPPED, all_time: 2204.582428(s)
[Aug-06-2025_01-09-51] - epoch:61, time: 25.144960(s), valid (NDCG@10: 0.3294, HR@10: 0.5505), test: SKIPPED, all_time: 2229.727388(s)
[Aug-06-2025_01-09-51] - 新的最佳性能: valid NDCG@10=0.3294, valid HR@10=0.5505
[Aug-06-2025_01-10-20] - epoch:62, time: 28.965371(s), valid (NDCG@10: 0.3240, HR@10: 0.5367), test: SKIPPED, all_time: 2258.692759(s)
[Aug-06-2025_01-10-56] - epoch:63, time: 36.504215(s), valid (NDCG@10: 0.3252, HR@10: 0.5459), test: SKIPPED, all_time: 2295.196975(s)
[Aug-06-2025_01-11-31] - epoch:64, time: 34.952182(s), valid (NDCG@10: 0.3295, HR@10: 0.5495), test: SKIPPED, all_time: 2330.149157(s)
[Aug-06-2025_01-12-06] - epoch:65, time: 35.230825(s), valid (NDCG@10: 0.3214, HR@10: 0.5330), test: SKIPPED, all_time: 2365.379982(s)
[Aug-06-2025_01-12-42] - epoch:66, time: 35.436670(s), valid (NDCG@10: 0.3239, HR@10: 0.5376), test: SKIPPED, all_time: 2400.816652(s)
[Aug-06-2025_01-13-17] - epoch:67, time: 35.580997(s), valid (NDCG@10: 0.3205, HR@10: 0.5413), test: SKIPPED, all_time: 2436.397648(s)
[Aug-06-2025_01-13-50] - epoch:68, time: 32.744627(s), valid (NDCG@10: 0.3232, HR@10: 0.5394), test: SKIPPED, all_time: 2469.142275(s)
[Aug-06-2025_01-14-14] - epoch:69, time: 23.722541(s), valid (NDCG@10: 0.3192, HR@10: 0.5294), test: SKIPPED, all_time: 2492.864815(s)
[Aug-06-2025_01-14-39] - epoch:70, time: 25.582748(s), valid (NDCG@10: 0.3278, HR@10: 0.5440), test: SKIPPED, all_time: 2518.447564(s)
[Aug-06-2025_01-15-06] - epoch:71, time: 26.945339(s), valid (NDCG@10: 0.3243, HR@10: 0.5468), test: SKIPPED, all_time: 2545.392903(s)
[Aug-06-2025_01-15-33] - epoch:72, time: 26.160741(s), valid (NDCG@10: 0.3156, HR@10: 0.5321), test: SKIPPED, all_time: 2571.553644(s)
[Aug-06-2025_01-15-59] - epoch:73, time: 26.444771(s), valid (NDCG@10: 0.3201, HR@10: 0.5404), test: SKIPPED, all_time: 2597.998415(s)
[Aug-06-2025_01-16-25] - epoch:74, time: 25.791399(s), valid (NDCG@10: 0.3221, HR@10: 0.5440), test: SKIPPED, all_time: 2623.789814(s)
[Aug-06-2025_01-16-52] - epoch:75, time: 27.128073(s), valid (NDCG@10: 0.3210, HR@10: 0.5404), test: SKIPPED, all_time: 2650.917887(s)
[Aug-06-2025_01-17-18] - epoch:76, time: 26.540364(s), valid (NDCG@10: 0.3221, HR@10: 0.5422), test: SKIPPED, all_time: 2677.458251(s)
[Aug-06-2025_01-17-44] - epoch:77, time: 25.800782(s), valid (NDCG@10: 0.3227, HR@10: 0.5422), test: SKIPPED, all_time: 2703.259033(s)
