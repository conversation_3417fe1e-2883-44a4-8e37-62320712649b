{'early_stop_enabled': True, 'model': 'BSARec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': False, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False, 'c': 10, 'alpha': 0.5}
[Aug-03-2025_21-11-33] - 开始训练，配置参数如下：
[Aug-03-2025_21-11-33] - early_stop_enabled: True
[Aug-03-2025_21-11-33] - model: BSARec
[Aug-03-2025_21-11-33] - lr: 0.001
[Aug-03-2025_21-11-33] - batch_size: 128
[Aug-03-2025_21-11-33] - neg_num: 99
[Aug-03-2025_21-11-33] - l2_reg: 0
[Aug-03-2025_21-11-33] - l2_emb: 0.0
[Aug-03-2025_21-11-33] - hidden_size: 50
[Aug-03-2025_21-11-33] - dropout: 0.2
[Aug-03-2025_21-11-33] - epochs: 1000000
[Aug-03-2025_21-11-33] - early_stop: 50
[Aug-03-2025_21-11-33] - datapath: ../../data/
[Aug-03-2025_21-11-33] - dataset: ml-100k
[Aug-03-2025_21-11-33] - train_data: ml-100k.txt
[Aug-03-2025_21-11-33] - log_path: ../log
[Aug-03-2025_21-11-33] - num_layers: 2
[Aug-03-2025_21-11-33] - num_heads: 1
[Aug-03-2025_21-11-33] - inner_size: 256
[Aug-03-2025_21-11-33] - max_seq_len: 200
[Aug-03-2025_21-11-33] - upload_mode: full
[Aug-03-2025_21-11-33] - skip_test_eval: False
[Aug-03-2025_21-11-33] - eval_freq: 1
[Aug-03-2025_21-11-33] - use_dynamic_sampling: False
[Aug-03-2025_21-11-33] - norm_first: False
[Aug-03-2025_21-11-33] - c: 10
[Aug-03-2025_21-11-33] - alpha: 0.5
[Aug-03-2025_21-11-33] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-03-2025_21-11-33] - 最大序列长度: 200
[Aug-03-2025_21-11-33] - 批次大小: 128
[Aug-03-2025_21-11-34] - 参数上传模式: full
[Aug-03-2025_21-11-34] - 隐私参数（本地更新）: []
[Aug-03-2025_21-11-34] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'encoder.blocks.0.layer.filter_layer.sqrt_beta', 'encoder.blocks.0.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.0.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.0.layer.attention_layer.query.weight', 'encoder.blocks.0.layer.attention_layer.query.bias', 'encoder.blocks.0.layer.attention_layer.key.weight', 'encoder.blocks.0.layer.attention_layer.key.bias', 'encoder.blocks.0.layer.attention_layer.value.weight', 'encoder.blocks.0.layer.attention_layer.value.bias', 'encoder.blocks.0.layer.attention_layer.dense.weight', 'encoder.blocks.0.layer.attention_layer.dense.bias', 'encoder.blocks.0.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.0.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.0.feed_forward.dense_1.weight', 'encoder.blocks.0.feed_forward.dense_1.bias', 'encoder.blocks.0.feed_forward.dense_2.weight', 'encoder.blocks.0.feed_forward.dense_2.bias', 'encoder.blocks.0.feed_forward.LayerNorm.weight', 'encoder.blocks.0.feed_forward.LayerNorm.bias', 'encoder.blocks.1.layer.filter_layer.sqrt_beta', 'encoder.blocks.1.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.1.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.1.layer.attention_layer.query.weight', 'encoder.blocks.1.layer.attention_layer.query.bias', 'encoder.blocks.1.layer.attention_layer.key.weight', 'encoder.blocks.1.layer.attention_layer.key.bias', 'encoder.blocks.1.layer.attention_layer.value.weight', 'encoder.blocks.1.layer.attention_layer.value.bias', 'encoder.blocks.1.layer.attention_layer.dense.weight', 'encoder.blocks.1.layer.attention_layer.dense.bias', 'encoder.blocks.1.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.1.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.1.feed_forward.dense_1.weight', 'encoder.blocks.1.feed_forward.dense_1.bias', 'encoder.blocks.1.feed_forward.dense_2.weight', 'encoder.blocks.1.feed_forward.dense_2.bias', 'encoder.blocks.1.feed_forward.LayerNorm.weight', 'encoder.blocks.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-03-2025_21-11-34] - 动态负采样: False
[Aug-03-2025_21-11-34] - 用户数量: 943
[Aug-03-2025_21-11-34] - 物品数量: 1349
[Aug-03-2025_21-12-07] - epoch:1, time: 19.045148(s), valid (NDCG@10: 0.1109, HR@10: 0.2312), test (NDCG@10: 0.0979, HR@10: 0.2078) all_time: 19.045148(s)
[Aug-03-2025_21-12-07] - 新的最佳性能: valid NDCG@10=0.1109, test NDCG@10=0.0979
[Aug-03-2025_21-12-38] - epoch:2, time: 19.370799(s), valid (NDCG@10: 0.1549, HR@10: 0.3234), test (NDCG@10: 0.1402, HR@10: 0.2757) all_time: 38.415947(s)
[Aug-03-2025_21-12-38] - 新的最佳性能: valid NDCG@10=0.1549, test NDCG@10=0.1402
[Aug-03-2025_21-13-29] - epoch:3, time: 29.349559(s), valid (NDCG@10: 0.1725, HR@10: 0.3446), test (NDCG@10: 0.1545, HR@10: 0.3150) all_time: 67.765507(s)
[Aug-03-2025_21-13-29] - 新的最佳性能: valid NDCG@10=0.1725, test NDCG@10=0.1545
[Aug-03-2025_21-14-06] - epoch:4, time: 27.092999(s), valid (NDCG@10: 0.1939, HR@10: 0.3712), test (NDCG@10: 0.1740, HR@10: 0.3330) all_time: 94.858505(s)
[Aug-03-2025_21-14-06] - 新的最佳性能: valid NDCG@10=0.1939, test NDCG@10=0.1740
[Aug-03-2025_21-14-38] - epoch:5, time: 21.489074(s), valid (NDCG@10: 0.2214, HR@10: 0.4040), test (NDCG@10: 0.2004, HR@10: 0.3606) all_time: 116.347579(s)
[Aug-03-2025_21-14-38] - 新的最佳性能: valid NDCG@10=0.2214, test NDCG@10=0.2004
[Aug-03-2025_21-15-06] - epoch:6, time: 17.151557(s), valid (NDCG@10: 0.2625, HR@10: 0.4655), test (NDCG@10: 0.2392, HR@10: 0.4358) all_time: 133.499136(s)
[Aug-03-2025_21-15-06] - 新的最佳性能: valid NDCG@10=0.2625, test NDCG@10=0.2392
[Aug-03-2025_21-15-35] - epoch:7, time: 16.843359(s), valid (NDCG@10: 0.2883, HR@10: 0.5080), test (NDCG@10: 0.2584, HR@10: 0.4740) all_time: 150.342495(s)
[Aug-03-2025_21-15-35] - 新的最佳性能: valid NDCG@10=0.2883, test NDCG@10=0.2584
[Aug-03-2025_21-16-09] - epoch:8, time: 19.666453(s), valid (NDCG@10: 0.2984, HR@10: 0.5048), test (NDCG@10: 0.2653, HR@10: 0.4889) all_time: 170.008948(s)
[Aug-03-2025_21-16-09] - 新的最佳性能: valid NDCG@10=0.2984, test NDCG@10=0.2653
[Aug-03-2025_21-16-37] - epoch:9, time: 17.075246(s), valid (NDCG@10: 0.3001, HR@10: 0.5133), test (NDCG@10: 0.2782, HR@10: 0.4973) all_time: 187.084194(s)
[Aug-03-2025_21-16-37] - 新的最佳性能: valid NDCG@10=0.3001, test NDCG@10=0.2782
[Aug-03-2025_21-17-04] - epoch:10, time: 16.933790(s), valid (NDCG@10: 0.3111, HR@10: 0.5313), test (NDCG@10: 0.2870, HR@10: 0.5101) all_time: 204.017984(s)
[Aug-03-2025_21-17-04] - 新的最佳性能: valid NDCG@10=0.3111, test NDCG@10=0.2870
[Aug-03-2025_21-17-32] - epoch:11, time: 18.174788(s), valid (NDCG@10: 0.3352, HR@10: 0.5684), test (NDCG@10: 0.2957, HR@10: 0.5196) all_time: 222.192773(s)
[Aug-03-2025_21-17-32] - 新的最佳性能: valid NDCG@10=0.3352, test NDCG@10=0.2957
[Aug-03-2025_21-18-03] - epoch:12, time: 18.484811(s), valid (NDCG@10: 0.3418, HR@10: 0.5716), test (NDCG@10: 0.2982, HR@10: 0.5429) all_time: 240.677583(s)
[Aug-03-2025_21-18-03] - 新的最佳性能: valid NDCG@10=0.3418, test NDCG@10=0.2982
[Aug-03-2025_21-18-39] - epoch:13, time: 21.502436(s), valid (NDCG@10: 0.3496, HR@10: 0.5790), test (NDCG@10: 0.3137, HR@10: 0.5631) all_time: 262.180019(s)
[Aug-03-2025_21-18-39] - 新的最佳性能: valid NDCG@10=0.3496, test NDCG@10=0.3137
[Aug-03-2025_21-19-12] - epoch:14, time: 21.324590(s), valid (NDCG@10: 0.3661, HR@10: 0.6055), test (NDCG@10: 0.3263, HR@10: 0.5811) all_time: 283.504609(s)
[Aug-03-2025_21-19-12] - 新的最佳性能: valid NDCG@10=0.3661, test NDCG@10=0.3263
[Aug-03-2025_21-19-46] - epoch:15, time: 20.288081(s), valid (NDCG@10: 0.3704, HR@10: 0.6246), test (NDCG@10: 0.3354, HR@10: 0.5970) all_time: 303.792691(s)
[Aug-03-2025_21-19-46] - 新的最佳性能: valid NDCG@10=0.3704, test NDCG@10=0.3354
[Aug-03-2025_21-20-30] - epoch:16, time: 22.713116(s), valid (NDCG@10: 0.3769, HR@10: 0.6246), test (NDCG@10: 0.3465, HR@10: 0.6140) all_time: 326.505806(s)
[Aug-03-2025_21-20-30] - 新的最佳性能: valid NDCG@10=0.3769, test NDCG@10=0.3465
[Aug-03-2025_21-21-03] - epoch:17, time: 20.969859(s), valid (NDCG@10: 0.3831, HR@10: 0.6490), test (NDCG@10: 0.3494, HR@10: 0.6204) all_time: 347.475665(s)
[Aug-03-2025_21-21-03] - 新的最佳性能: valid NDCG@10=0.3831, test NDCG@10=0.3494
[Aug-03-2025_21-21-36] - epoch:18, time: 21.228069(s), valid (NDCG@10: 0.3946, HR@10: 0.6564), test (NDCG@10: 0.3581, HR@10: 0.6331) all_time: 368.703734(s)
[Aug-03-2025_21-21-36] - 新的最佳性能: valid NDCG@10=0.3946, test NDCG@10=0.3581
[Aug-03-2025_21-22-08] - epoch:19, time: 20.131376(s), valid (NDCG@10: 0.3953, HR@10: 0.6681), test (NDCG@10: 0.3624, HR@10: 0.6394) all_time: 388.835109(s)
[Aug-03-2025_21-22-08] - 新的最佳性能: valid NDCG@10=0.3953, test NDCG@10=0.3624
[Aug-03-2025_21-22-40] - epoch:20, time: 18.547897(s), valid (NDCG@10: 0.3959, HR@10: 0.6691), test (NDCG@10: 0.3696, HR@10: 0.6288) all_time: 407.383006(s)
[Aug-03-2025_21-22-40] - 新的最佳性能: valid NDCG@10=0.3959, test NDCG@10=0.3696
[Aug-03-2025_21-23-12] - epoch:21, time: 19.553219(s), valid (NDCG@10: 0.3948, HR@10: 0.6564), test (NDCG@10: 0.3582, HR@10: 0.6341) all_time: 426.936225(s)
[Aug-03-2025_21-23-51] - epoch:22, time: 23.321158(s), valid (NDCG@10: 0.4031, HR@10: 0.6638), test (NDCG@10: 0.3694, HR@10: 0.6363) all_time: 450.257383(s)
[Aug-03-2025_21-24-41] - epoch:23, time: 31.138308(s), valid (NDCG@10: 0.4095, HR@10: 0.6829), test (NDCG@10: 0.3668, HR@10: 0.6501) all_time: 481.395691(s)
[Aug-03-2025_21-24-41] - 新的最佳性能: valid NDCG@10=0.4095, test NDCG@10=0.3696
[Aug-03-2025_21-25-14] - epoch:24, time: 21.923705(s), valid (NDCG@10: 0.4040, HR@10: 0.6691), test (NDCG@10: 0.3691, HR@10: 0.6479) all_time: 503.319396(s)
[Aug-03-2025_21-25-44] - epoch:25, time: 18.055441(s), valid (NDCG@10: 0.4082, HR@10: 0.6755), test (NDCG@10: 0.3769, HR@10: 0.6426) all_time: 521.374837(s)
[Aug-03-2025_21-25-44] - 新的最佳性能: valid NDCG@10=0.4095, test NDCG@10=0.3769
[Aug-03-2025_21-26-21] - epoch:26, time: 22.746446(s), valid (NDCG@10: 0.4055, HR@10: 0.6882), test (NDCG@10: 0.3776, HR@10: 0.6448) all_time: 544.121282(s)
[Aug-03-2025_21-26-21] - 新的最佳性能: valid NDCG@10=0.4095, test NDCG@10=0.3776
[Aug-03-2025_21-26-58] - epoch:27, time: 23.637345(s), valid (NDCG@10: 0.4150, HR@10: 0.6903), test (NDCG@10: 0.3736, HR@10: 0.6532) all_time: 567.758627(s)
[Aug-03-2025_21-26-58] - 新的最佳性能: valid NDCG@10=0.4150, test NDCG@10=0.3776
[Aug-03-2025_21-27-33] - epoch:28, time: 23.583271(s), valid (NDCG@10: 0.4189, HR@10: 0.6882), test (NDCG@10: 0.3806, HR@10: 0.6564) all_time: 591.341897(s)
[Aug-03-2025_21-27-33] - 新的最佳性能: valid NDCG@10=0.4189, test NDCG@10=0.3806
[Aug-03-2025_21-28-03] - epoch:29, time: 18.221566(s), valid (NDCG@10: 0.4151, HR@10: 0.6967), test (NDCG@10: 0.3864, HR@10: 0.6575) all_time: 609.563463(s)
[Aug-03-2025_21-28-03] - 新的最佳性能: valid NDCG@10=0.4189, test NDCG@10=0.3864
[Aug-03-2025_21-28-44] - epoch:30, time: 20.173457(s), valid (NDCG@10: 0.4157, HR@10: 0.6946), test (NDCG@10: 0.3721, HR@10: 0.6585) all_time: 629.736920(s)
[Aug-03-2025_21-28-44] - 新的最佳性能: valid NDCG@10=0.4189, test NDCG@10=0.3864
[Aug-03-2025_21-29-23] - epoch:31, time: 22.870266(s), valid (NDCG@10: 0.4235, HR@10: 0.7031), test (NDCG@10: 0.3891, HR@10: 0.6723) all_time: 652.607186(s)
[Aug-03-2025_21-29-23] - 新的最佳性能: valid NDCG@10=0.4235, test NDCG@10=0.3891
[Aug-03-2025_21-29-56] - epoch:32, time: 21.162147(s), valid (NDCG@10: 0.4286, HR@10: 0.7020), test (NDCG@10: 0.3948, HR@10: 0.6628) all_time: 673.769333(s)
[Aug-03-2025_21-29-56] - 新的最佳性能: valid NDCG@10=0.4286, test NDCG@10=0.3948
[Aug-03-2025_21-30-27] - epoch:33, time: 18.113291(s), valid (NDCG@10: 0.4206, HR@10: 0.6978), test (NDCG@10: 0.3867, HR@10: 0.6681) all_time: 691.882624(s)
[Aug-03-2025_21-31-02] - epoch:34, time: 22.873056(s), valid (NDCG@10: 0.4283, HR@10: 0.7147), test (NDCG@10: 0.3851, HR@10: 0.6554) all_time: 714.755680(s)
[Aug-03-2025_21-31-02] - 新的最佳性能: valid NDCG@10=0.4286, test NDCG@10=0.3948
[Aug-03-2025_21-31-37] - epoch:35, time: 21.129827(s), valid (NDCG@10: 0.4345, HR@10: 0.7084), test (NDCG@10: 0.3854, HR@10: 0.6617) all_time: 735.885507(s)
[Aug-03-2025_21-32-21] - epoch:36, time: 24.334594(s), valid (NDCG@10: 0.4358, HR@10: 0.7105), test (NDCG@10: 0.3887, HR@10: 0.6564) all_time: 760.220101(s)
[Aug-03-2025_21-32-55] - epoch:37, time: 23.061978(s), valid (NDCG@10: 0.4356, HR@10: 0.7084), test (NDCG@10: 0.3885, HR@10: 0.6585) all_time: 783.282079(s)
[Aug-03-2025_21-33-26] - epoch:38, time: 18.489163(s), valid (NDCG@10: 0.4346, HR@10: 0.7179), test (NDCG@10: 0.3927, HR@10: 0.6670) all_time: 801.771242(s)
[Aug-03-2025_21-33-26] - 新的最佳性能: valid NDCG@10=0.4358, test NDCG@10=0.3948
[Aug-03-2025_21-33-57] - epoch:39, time: 19.192424(s), valid (NDCG@10: 0.4346, HR@10: 0.7105), test (NDCG@10: 0.3924, HR@10: 0.6691) all_time: 820.963666(s)
[Aug-03-2025_21-34-43] - epoch:40, time: 25.707460(s), valid (NDCG@10: 0.4342, HR@10: 0.7200), test (NDCG@10: 0.3919, HR@10: 0.6638) all_time: 846.671126(s)
[Aug-03-2025_21-34-43] - 新的最佳性能: valid NDCG@10=0.4358, test NDCG@10=0.3948
[Aug-03-2025_21-35-11] - epoch:41, time: 17.242611(s), valid (NDCG@10: 0.4322, HR@10: 0.7031), test (NDCG@10: 0.3941, HR@10: 0.6734) all_time: 863.913737(s)
[Aug-03-2025_21-35-11] - 新的最佳性能: valid NDCG@10=0.4358, test NDCG@10=0.3948
[Aug-03-2025_21-35-49] - epoch:42, time: 22.568653(s), valid (NDCG@10: 0.4433, HR@10: 0.7190), test (NDCG@10: 0.3912, HR@10: 0.6628) all_time: 886.482389(s)
[Aug-03-2025_21-36-34] - epoch:43, time: 26.467137(s), valid (NDCG@10: 0.4363, HR@10: 0.7137), test (NDCG@10: 0.3952, HR@10: 0.6691) all_time: 912.949526(s)
[Aug-03-2025_21-36-34] - 新的最佳性能: valid NDCG@10=0.4433, test NDCG@10=0.3952
[Aug-03-2025_21-37-08] - epoch:44, time: 21.641301(s), valid (NDCG@10: 0.4397, HR@10: 0.7158), test (NDCG@10: 0.3945, HR@10: 0.6607) all_time: 934.590827(s)
[Aug-03-2025_21-37-37] - epoch:45, time: 17.587443(s), valid (NDCG@10: 0.4395, HR@10: 0.7190), test (NDCG@10: 0.3974, HR@10: 0.6564) all_time: 952.178270(s)
[Aug-03-2025_21-37-37] - 新的最佳性能: valid NDCG@10=0.4433, test NDCG@10=0.3974
[Aug-03-2025_21-38-11] - epoch:46, time: 21.803842(s), valid (NDCG@10: 0.4352, HR@10: 0.7126), test (NDCG@10: 0.3949, HR@10: 0.6522) all_time: 973.982111(s)
[Aug-03-2025_21-38-55] - epoch:47, time: 26.503029(s), valid (NDCG@10: 0.4346, HR@10: 0.7200), test (NDCG@10: 0.3849, HR@10: 0.6575) all_time: 1000.485140(s)
[Aug-03-2025_21-39-35] - epoch:48, time: 28.060140(s), valid (NDCG@10: 0.4432, HR@10: 0.7137), test (NDCG@10: 0.3936, HR@10: 0.6596) all_time: 1028.545280(s)
[Aug-03-2025_21-40-12] - epoch:49, time: 22.763101(s), valid (NDCG@10: 0.4374, HR@10: 0.7137), test (NDCG@10: 0.3954, HR@10: 0.6649) all_time: 1051.308381(s)
[Aug-03-2025_21-40-44] - epoch:50, time: 19.422168(s), valid (NDCG@10: 0.4364, HR@10: 0.7010), test (NDCG@10: 0.3908, HR@10: 0.6554) all_time: 1070.730549(s)
[Aug-03-2025_21-41-13] - epoch:51, time: 17.346786(s), valid (NDCG@10: 0.4359, HR@10: 0.7200), test (NDCG@10: 0.3950, HR@10: 0.6628) all_time: 1088.077335(s)
[Aug-03-2025_21-41-45] - epoch:52, time: 20.076458(s), valid (NDCG@10: 0.4357, HR@10: 0.6999), test (NDCG@10: 0.3932, HR@10: 0.6490) all_time: 1108.153793(s)
[Aug-03-2025_21-42-14] - epoch:53, time: 17.853138(s), valid (NDCG@10: 0.4321, HR@10: 0.6999), test (NDCG@10: 0.3929, HR@10: 0.6585) all_time: 1126.006931(s)
[Aug-03-2025_21-42-45] - epoch:54, time: 19.569536(s), valid (NDCG@10: 0.4445, HR@10: 0.7105), test (NDCG@10: 0.3996, HR@10: 0.6607) all_time: 1145.576468(s)
[Aug-03-2025_21-42-45] - 新的最佳性能: valid NDCG@10=0.4445, test NDCG@10=0.3996
[Aug-03-2025_21-43-17] - epoch:55, time: 20.018354(s), valid (NDCG@10: 0.4379, HR@10: 0.6988), test (NDCG@10: 0.3848, HR@10: 0.6448) all_time: 1165.594822(s)
[Aug-03-2025_21-43-45] - epoch:56, time: 17.324918(s), valid (NDCG@10: 0.4460, HR@10: 0.7190), test (NDCG@10: 0.3867, HR@10: 0.6405) all_time: 1182.919740(s)
[Aug-03-2025_21-44-18] - epoch:57, time: 21.704089(s), valid (NDCG@10: 0.4373, HR@10: 0.7158), test (NDCG@10: 0.3912, HR@10: 0.6532) all_time: 1204.623830(s)
[Aug-03-2025_21-44-48] - epoch:58, time: 18.908958(s), valid (NDCG@10: 0.4304, HR@10: 0.7052), test (NDCG@10: 0.3938, HR@10: 0.6532) all_time: 1223.532788(s)
[Aug-03-2025_21-45-16] - epoch:59, time: 16.594246(s), valid (NDCG@10: 0.4394, HR@10: 0.7052), test (NDCG@10: 0.3873, HR@10: 0.6458) all_time: 1240.127033(s)
[Aug-03-2025_21-45-53] - epoch:60, time: 24.566139(s), valid (NDCG@10: 0.4471, HR@10: 0.7253), test (NDCG@10: 0.3945, HR@10: 0.6543) all_time: 1264.693172(s)
[Aug-03-2025_21-45-53] - 新的最佳性能: valid NDCG@10=0.4471, test NDCG@10=0.3996
[Aug-03-2025_21-46-22] - epoch:61, time: 17.362521(s), valid (NDCG@10: 0.4407, HR@10: 0.7137), test (NDCG@10: 0.3827, HR@10: 0.6426) all_time: 1282.055693(s)
[Aug-03-2025_21-46-51] - epoch:62, time: 18.092248(s), valid (NDCG@10: 0.4451, HR@10: 0.7169), test (NDCG@10: 0.3927, HR@10: 0.6363) all_time: 1300.147941(s)
[Aug-03-2025_21-47-22] - epoch:63, time: 19.336658(s), valid (NDCG@10: 0.4311, HR@10: 0.7031), test (NDCG@10: 0.3951, HR@10: 0.6585) all_time: 1319.484599(s)
[Aug-03-2025_21-48-01] - epoch:64, time: 20.110943(s), valid (NDCG@10: 0.4345, HR@10: 0.7169), test (NDCG@10: 0.3881, HR@10: 0.6373) all_time: 1339.595542(s)
[Aug-03-2025_21-49-22] - epoch:65, time: 55.373478(s), valid (NDCG@10: 0.4277, HR@10: 0.6978), test (NDCG@10: 0.3871, HR@10: 0.6543) all_time: 1394.969020(s)
[Aug-03-2025_21-50-37] - epoch:66, time: 43.823354(s), valid (NDCG@10: 0.4295, HR@10: 0.6978), test (NDCG@10: 0.3848, HR@10: 0.6405) all_time: 1438.792374(s)
[Aug-03-2025_21-51-47] - epoch:67, time: 41.702160(s), valid (NDCG@10: 0.4346, HR@10: 0.7020), test (NDCG@10: 0.3882, HR@10: 0.6479) all_time: 1480.494534(s)
[Aug-03-2025_21-52-55] - epoch:68, time: 41.632736(s), valid (NDCG@10: 0.4313, HR@10: 0.7052), test (NDCG@10: 0.3898, HR@10: 0.6448) all_time: 1522.127270(s)
[Aug-03-2025_21-54-02] - epoch:69, time: 43.090313(s), valid (NDCG@10: 0.4301, HR@10: 0.6978), test (NDCG@10: 0.3827, HR@10: 0.6341) all_time: 1565.217583(s)
[Aug-03-2025_21-54-57] - epoch:70, time: 32.135092(s), valid (NDCG@10: 0.4379, HR@10: 0.7063), test (NDCG@10: 0.3843, HR@10: 0.6320) all_time: 1597.352676(s)
[Aug-03-2025_21-55-51] - epoch:71, time: 31.871634(s), valid (NDCG@10: 0.4384, HR@10: 0.7116), test (NDCG@10: 0.3861, HR@10: 0.6373) all_time: 1629.224309(s)
[Aug-03-2025_21-56-46] - epoch:72, time: 32.480554(s), valid (NDCG@10: 0.4360, HR@10: 0.7063), test (NDCG@10: 0.3785, HR@10: 0.6384) all_time: 1661.704864(s)
[Aug-03-2025_21-57-39] - epoch:73, time: 31.482016(s), valid (NDCG@10: 0.4287, HR@10: 0.6978), test (NDCG@10: 0.3836, HR@10: 0.6394) all_time: 1693.186880(s)
[Aug-03-2025_21-58-29] - epoch:74, time: 29.722581(s), valid (NDCG@10: 0.4370, HR@10: 0.6988), test (NDCG@10: 0.3814, HR@10: 0.6299) all_time: 1722.909461(s)
[Aug-03-2025_21-59-22] - epoch:75, time: 31.892593(s), valid (NDCG@10: 0.4287, HR@10: 0.7052), test (NDCG@10: 0.3766, HR@10: 0.6267) all_time: 1754.802054(s)
[Aug-03-2025_22-00-12] - epoch:76, time: 29.358452(s), valid (NDCG@10: 0.4326, HR@10: 0.6946), test (NDCG@10: 0.3757, HR@10: 0.6331) all_time: 1784.160506(s)
[Aug-03-2025_22-01-02] - epoch:77, time: 30.513233(s), valid (NDCG@10: 0.4309, HR@10: 0.7063), test (NDCG@10: 0.3855, HR@10: 0.6511) all_time: 1814.673739(s)
[Aug-03-2025_22-01-53] - epoch:78, time: 30.700669(s), valid (NDCG@10: 0.4215, HR@10: 0.6829), test (NDCG@10: 0.3853, HR@10: 0.6320) all_time: 1845.374407(s)
[Aug-03-2025_22-02-44] - epoch:79, time: 30.154067(s), valid (NDCG@10: 0.4298, HR@10: 0.7052), test (NDCG@10: 0.3862, HR@10: 0.6458) all_time: 1875.528474(s)
[Aug-03-2025_22-03-35] - epoch:80, time: 29.975401(s), valid (NDCG@10: 0.4244, HR@10: 0.7031), test (NDCG@10: 0.3831, HR@10: 0.6405) all_time: 1905.503875(s)
[Aug-03-2025_22-04-26] - epoch:81, time: 30.145977(s), valid (NDCG@10: 0.4284, HR@10: 0.7041), test (NDCG@10: 0.3820, HR@10: 0.6331) all_time: 1935.649852(s)
[Aug-03-2025_22-05-16] - epoch:82, time: 29.873275(s), valid (NDCG@10: 0.4225, HR@10: 0.6893), test (NDCG@10: 0.3870, HR@10: 0.6437) all_time: 1965.523126(s)
[Aug-03-2025_22-06-07] - epoch:83, time: 30.747341(s), valid (NDCG@10: 0.4245, HR@10: 0.6840), test (NDCG@10: 0.3797, HR@10: 0.6341) all_time: 1996.270467(s)
[Aug-03-2025_22-06-57] - epoch:84, time: 30.446305(s), valid (NDCG@10: 0.4299, HR@10: 0.7020), test (NDCG@10: 0.3823, HR@10: 0.6363) all_time: 2026.716772(s)
[Aug-03-2025_22-07-47] - epoch:85, time: 29.030881(s), valid (NDCG@10: 0.4162, HR@10: 0.6819), test (NDCG@10: 0.3760, HR@10: 0.6246) all_time: 2055.747653(s)
[Aug-03-2025_22-08-39] - epoch:86, time: 30.238140(s), valid (NDCG@10: 0.4228, HR@10: 0.6882), test (NDCG@10: 0.3908, HR@10: 0.6341) all_time: 2085.985793(s)
[Aug-03-2025_22-09-29] - epoch:87, time: 29.635034(s), valid (NDCG@10: 0.4246, HR@10: 0.6935), test (NDCG@10: 0.3816, HR@10: 0.6373) all_time: 2115.620826(s)
[Aug-03-2025_22-10-20] - epoch:88, time: 29.387254(s), valid (NDCG@10: 0.4225, HR@10: 0.6903), test (NDCG@10: 0.3796, HR@10: 0.6257) all_time: 2145.008080(s)
[Aug-03-2025_22-11-11] - epoch:89, time: 31.442225(s), valid (NDCG@10: 0.4187, HR@10: 0.6819), test (NDCG@10: 0.3718, HR@10: 0.6310) all_time: 2176.450305(s)
[Aug-03-2025_22-12-01] - epoch:90, time: 29.056649(s), valid (NDCG@10: 0.4169, HR@10: 0.6882), test (NDCG@10: 0.3753, HR@10: 0.6235) all_time: 2205.506954(s)
[Aug-03-2025_22-12-46] - epoch:91, time: 28.470040(s), valid (NDCG@10: 0.4259, HR@10: 0.6882), test (NDCG@10: 0.3801, HR@10: 0.6320) all_time: 2233.976994(s)
[Aug-03-2025_22-13-16] - epoch:92, time: 17.997532(s), valid (NDCG@10: 0.4195, HR@10: 0.6882), test (NDCG@10: 0.3814, HR@10: 0.6235) all_time: 2251.974527(s)
[Aug-03-2025_22-13-56] - epoch:93, time: 23.505094(s), valid (NDCG@10: 0.4184, HR@10: 0.6755), test (NDCG@10: 0.3740, HR@10: 0.6278) all_time: 2275.479620(s)
[Aug-03-2025_22-14-36] - epoch:94, time: 24.772790(s), valid (NDCG@10: 0.4214, HR@10: 0.6819), test (NDCG@10: 0.3701, HR@10: 0.6214) all_time: 2300.252410(s)
[Aug-03-2025_22-15-15] - epoch:95, time: 23.754658(s), valid (NDCG@10: 0.4241, HR@10: 0.6776), test (NDCG@10: 0.3796, HR@10: 0.6235) all_time: 2324.007068(s)
[Aug-03-2025_22-15-55] - epoch:96, time: 23.994003(s), valid (NDCG@10: 0.4218, HR@10: 0.6861), test (NDCG@10: 0.3766, HR@10: 0.6235) all_time: 2348.001071(s)
[Aug-03-2025_22-16-35] - epoch:97, time: 24.235358(s), valid (NDCG@10: 0.4144, HR@10: 0.6797), test (NDCG@10: 0.3669, HR@10: 0.6045) all_time: 2372.236429(s)
[Aug-03-2025_22-17-09] - epoch:98, time: 23.233075(s), valid (NDCG@10: 0.4095, HR@10: 0.6819), test (NDCG@10: 0.3711, HR@10: 0.6225) all_time: 2395.469504(s)
[Aug-03-2025_22-17-40] - epoch:99, time: 18.350903(s), valid (NDCG@10: 0.4150, HR@10: 0.6840), test (NDCG@10: 0.3755, HR@10: 0.6267) all_time: 2413.820407(s)
[Aug-03-2025_22-18-09] - epoch:100, time: 18.425734(s), valid (NDCG@10: 0.4125, HR@10: 0.6797), test (NDCG@10: 0.3682, HR@10: 0.6151) all_time: 2432.246141(s)
[Aug-03-2025_22-18-36] - epoch:101, time: 16.258220(s), valid (NDCG@10: 0.4072, HR@10: 0.6744), test (NDCG@10: 0.3625, HR@10: 0.6151) all_time: 2448.504362(s)
