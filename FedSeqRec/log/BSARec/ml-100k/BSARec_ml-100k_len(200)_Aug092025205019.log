{'early_stop_enabled': True, 'model': 'BSARec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000, 'early_stop': 30, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': False, 'eval_freq': 1, 'c': 9, 'alpha': 0.3}
[Aug-09-2025_20-50-19] - 开始训练，配置参数如下：
[Aug-09-2025_20-50-19] - early_stop_enabled: True
[Aug-09-2025_20-50-19] - model: BSARec
[Aug-09-2025_20-50-19] - lr: 0.001
[Aug-09-2025_20-50-19] - batch_size: 128
[Aug-09-2025_20-50-19] - neg_num: 99
[Aug-09-2025_20-50-19] - l2_reg: 0
[Aug-09-2025_20-50-19] - l2_emb: 0.0
[Aug-09-2025_20-50-19] - hidden_size: 50
[Aug-09-2025_20-50-19] - dropout: 0.2
[Aug-09-2025_20-50-19] - epochs: 1000
[Aug-09-2025_20-50-19] - early_stop: 30
[Aug-09-2025_20-50-19] - datapath: ../../data/
[Aug-09-2025_20-50-19] - dataset: ml-100k
[Aug-09-2025_20-50-19] - train_data: ml-100k.txt
[Aug-09-2025_20-50-19] - log_path: ../log
[Aug-09-2025_20-50-19] - num_layers: 2
[Aug-09-2025_20-50-19] - num_heads: 1
[Aug-09-2025_20-50-19] - inner_size: 256
[Aug-09-2025_20-50-19] - max_seq_len: 200
[Aug-09-2025_20-50-19] - upload_mode: full
[Aug-09-2025_20-50-19] - skip_test_eval: False
[Aug-09-2025_20-50-19] - eval_freq: 1
[Aug-09-2025_20-50-19] - c: 9
[Aug-09-2025_20-50-19] - alpha: 0.3
[Aug-09-2025_20-50-19] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-09-2025_20-50-19] - 最大序列长度: 200
[Aug-09-2025_20-50-19] - 批次大小: 128
[Aug-09-2025_20-50-22] - 参数上传模式: full
[Aug-09-2025_20-50-22] - 隐私参数（本地更新）: []
[Aug-09-2025_20-50-22] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'encoder.blocks.0.layer.filter_layer.sqrt_beta', 'encoder.blocks.0.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.0.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.0.layer.attention_layer.query.weight', 'encoder.blocks.0.layer.attention_layer.query.bias', 'encoder.blocks.0.layer.attention_layer.key.weight', 'encoder.blocks.0.layer.attention_layer.key.bias', 'encoder.blocks.0.layer.attention_layer.value.weight', 'encoder.blocks.0.layer.attention_layer.value.bias', 'encoder.blocks.0.layer.attention_layer.dense.weight', 'encoder.blocks.0.layer.attention_layer.dense.bias', 'encoder.blocks.0.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.0.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.0.feed_forward.dense_1.weight', 'encoder.blocks.0.feed_forward.dense_1.bias', 'encoder.blocks.0.feed_forward.dense_2.weight', 'encoder.blocks.0.feed_forward.dense_2.bias', 'encoder.blocks.0.feed_forward.LayerNorm.weight', 'encoder.blocks.0.feed_forward.LayerNorm.bias', 'encoder.blocks.1.layer.filter_layer.sqrt_beta', 'encoder.blocks.1.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.1.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.1.layer.attention_layer.query.weight', 'encoder.blocks.1.layer.attention_layer.query.bias', 'encoder.blocks.1.layer.attention_layer.key.weight', 'encoder.blocks.1.layer.attention_layer.key.bias', 'encoder.blocks.1.layer.attention_layer.value.weight', 'encoder.blocks.1.layer.attention_layer.value.bias', 'encoder.blocks.1.layer.attention_layer.dense.weight', 'encoder.blocks.1.layer.attention_layer.dense.bias', 'encoder.blocks.1.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.1.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.1.feed_forward.dense_1.weight', 'encoder.blocks.1.feed_forward.dense_1.bias', 'encoder.blocks.1.feed_forward.dense_2.weight', 'encoder.blocks.1.feed_forward.dense_2.bias', 'encoder.blocks.1.feed_forward.LayerNorm.weight', 'encoder.blocks.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-09-2025_20-50-22] - 用户数量: 943
[Aug-09-2025_20-50-22] - 物品数量: 1349
[Aug-09-2025_20-50-26] -  一个用户平均参数大小为: 602248.0000 B
[Aug-09-2025_20-50-26] -  一个用户平均上传延迟为: 0.1149 s (速度: 5MB/s)
[Aug-09-2025_20-50-47] -  943个客户端参数大小合计为: 567919864.0000 B
[Aug-09-2025_20-50-47] -  943个客户端上传延迟合计为: 108.3221 s (速度: 5MB/s)
[Aug-09-2025_20-51-06] - epoch:1, time: 44.155421(s), valid (NDCG@10: 0.1008, HR@10: 0.2131), test (NDCG@10: 0.0991, HR@10: 0.2100) all_time: 44.155421(s)
[Aug-09-2025_20-51-06] - 新的最佳性能: valid NDCG@10=0.1008, test NDCG@10=0.0991
