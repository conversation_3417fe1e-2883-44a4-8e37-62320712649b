{'early_stop_enabled': True, 'model': 'BSARec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000, 'early_stop': 30, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': False, 'eval_freq': 1, 'download_speed': 5.0, 'upload_speed': 5.0, 'c': 9, 'alpha': 0.3}
[Aug-18-2025_12-15-00] - 开始训练，配置参数如下：
[Aug-18-2025_12-15-00] - early_stop_enabled: True
[Aug-18-2025_12-15-00] - model: BSARec
[Aug-18-2025_12-15-00] - lr: 0.001
[Aug-18-2025_12-15-00] - batch_size: 128
[Aug-18-2025_12-15-00] - neg_num: 99
[Aug-18-2025_12-15-00] - l2_reg: 0
[Aug-18-2025_12-15-00] - l2_emb: 0.0
[Aug-18-2025_12-15-00] - hidden_size: 50
[Aug-18-2025_12-15-00] - dropout: 0.2
[Aug-18-2025_12-15-00] - epochs: 1000
[Aug-18-2025_12-15-00] - early_stop: 30
[Aug-18-2025_12-15-00] - datapath: ../../data/
[Aug-18-2025_12-15-00] - dataset: ml-100k
[Aug-18-2025_12-15-00] - train_data: ml-100k.txt
[Aug-18-2025_12-15-00] - log_path: ../log
[Aug-18-2025_12-15-00] - num_layers: 2
[Aug-18-2025_12-15-00] - num_heads: 1
[Aug-18-2025_12-15-00] - inner_size: 256
[Aug-18-2025_12-15-00] - max_seq_len: 200
[Aug-18-2025_12-15-00] - upload_mode: full
[Aug-18-2025_12-15-00] - skip_test_eval: False
[Aug-18-2025_12-15-00] - eval_freq: 1
[Aug-18-2025_12-15-00] - download_speed: 5.0
[Aug-18-2025_12-15-00] - upload_speed: 5.0
[Aug-18-2025_12-15-00] - c: 9
[Aug-18-2025_12-15-00] - alpha: 0.3
[Aug-18-2025_12-15-00] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-18-2025_12-15-00] - 最大序列长度: 200
[Aug-18-2025_12-15-00] - 批次大小: 128
