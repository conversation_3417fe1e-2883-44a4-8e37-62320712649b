{'early_stop_enabled': True, 'model': 'BSARec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000, 'early_stop': 30, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': False, 'eval_freq': 1, 'c': 9, 'alpha': 0.3}
[Aug-09-2025_20-34-08] - 开始训练，配置参数如下：
[Aug-09-2025_20-34-08] - early_stop_enabled: True
[Aug-09-2025_20-34-08] - model: BSARec
[Aug-09-2025_20-34-08] - lr: 0.001
[Aug-09-2025_20-34-08] - batch_size: 128
[Aug-09-2025_20-34-08] - neg_num: 99
[Aug-09-2025_20-34-08] - l2_reg: 0
[Aug-09-2025_20-34-08] - l2_emb: 0.0
[Aug-09-2025_20-34-08] - hidden_size: 50
[Aug-09-2025_20-34-08] - dropout: 0.2
[Aug-09-2025_20-34-08] - epochs: 1000
[Aug-09-2025_20-34-08] - early_stop: 30
[Aug-09-2025_20-34-08] - datapath: ../../data/
[Aug-09-2025_20-34-08] - dataset: ml-100k
[Aug-09-2025_20-34-08] - train_data: ml-100k
[Aug-09-2025_20-34-08] - log_path: ../log
[Aug-09-2025_20-34-08] - num_layers: 2
[Aug-09-2025_20-34-08] - num_heads: 1
[Aug-09-2025_20-34-08] - inner_size: 256
[Aug-09-2025_20-34-08] - max_seq_len: 200
[Aug-09-2025_20-34-08] - upload_mode: full
[Aug-09-2025_20-34-08] - skip_test_eval: False
[Aug-09-2025_20-34-08] - eval_freq: 1
[Aug-09-2025_20-34-08] - c: 9
[Aug-09-2025_20-34-08] - alpha: 0.3
[Aug-09-2025_20-34-08] - 训练数据: ../../data/ml-100k/ml-100k
[Aug-09-2025_20-34-08] - 最大序列长度: 200
[Aug-09-2025_20-34-08] - 批次大小: 128
