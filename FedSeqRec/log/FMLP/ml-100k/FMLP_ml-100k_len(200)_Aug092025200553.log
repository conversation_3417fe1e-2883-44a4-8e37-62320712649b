{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000, 'early_stop': 30, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': False, 'eval_freq': 1, 'c': 9, 'alpha': 0.3}
[Aug-09-2025_20-05-53] - 开始训练，配置参数如下：
[Aug-09-2025_20-05-53] - early_stop_enabled: True
[Aug-09-2025_20-05-53] - model: FMLP
[Aug-09-2025_20-05-53] - lr: 0.001
[Aug-09-2025_20-05-53] - batch_size: 128
[Aug-09-2025_20-05-53] - neg_num: 99
[Aug-09-2025_20-05-53] - l2_reg: 0
[Aug-09-2025_20-05-53] - l2_emb: 0.0
[Aug-09-2025_20-05-53] - hidden_size: 50
[Aug-09-2025_20-05-53] - dropout: 0.2
[Aug-09-2025_20-05-53] - epochs: 1000
[Aug-09-2025_20-05-53] - early_stop: 30
[Aug-09-2025_20-05-53] - datapath: ../../data/
[Aug-09-2025_20-05-53] - dataset: ml-100k
[Aug-09-2025_20-05-53] - train_data: ml-100k.txt
[Aug-09-2025_20-05-53] - log_path: ../log
[Aug-09-2025_20-05-53] - num_layers: 2
[Aug-09-2025_20-05-53] - num_heads: 1
[Aug-09-2025_20-05-53] - inner_size: 256
[Aug-09-2025_20-05-53] - max_seq_len: 200
[Aug-09-2025_20-05-53] - upload_mode: full
[Aug-09-2025_20-05-53] - skip_test_eval: False
[Aug-09-2025_20-05-53] - eval_freq: 1
[Aug-09-2025_20-05-53] - c: 9
[Aug-09-2025_20-05-53] - alpha: 0.3
[Aug-09-2025_20-05-53] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-09-2025_20-05-53] - 最大序列长度: 200
[Aug-09-2025_20-05-53] - 批次大小: 128
[Aug-09-2025_20-05-55] - 参数上传模式: full
[Aug-09-2025_20-05-55] - 隐私参数（本地更新）: []
[Aug-09-2025_20-05-55] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'trm_encoder.layer.0.filter_layer.complex_weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.bias', 'trm_encoder.layer.0.feed_forward.dense_1.weight', 'trm_encoder.layer.0.feed_forward.dense_1.bias', 'trm_encoder.layer.0.feed_forward.dense_2.weight', 'trm_encoder.layer.0.feed_forward.dense_2.bias', 'trm_encoder.layer.0.feed_forward.LayerNorm.weight', 'trm_encoder.layer.0.feed_forward.LayerNorm.bias', 'trm_encoder.layer.1.filter_layer.complex_weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.bias', 'trm_encoder.layer.1.feed_forward.dense_1.weight', 'trm_encoder.layer.1.feed_forward.dense_1.bias', 'trm_encoder.layer.1.feed_forward.dense_2.weight', 'trm_encoder.layer.1.feed_forward.dense_2.bias', 'trm_encoder.layer.1.feed_forward.LayerNorm.weight', 'trm_encoder.layer.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-09-2025_20-05-55] - 用户数量: 943
[Aug-09-2025_20-05-55] - 物品数量: 1349
[Aug-09-2025_20-05-57] -  用户参数总大小为: 75031.00 KB
[Aug-09-2025_20-06-13] - epoch:1, time: 17.809516(s), valid (NDCG@10: 0.0946, HR@10: 0.1813), test (NDCG@10: 0.0891, HR@10: 0.1718) all_time: 17.809516(s)
[Aug-09-2025_20-06-13] - 新的最佳性能: valid NDCG@10=0.0946, test NDCG@10=0.0891
