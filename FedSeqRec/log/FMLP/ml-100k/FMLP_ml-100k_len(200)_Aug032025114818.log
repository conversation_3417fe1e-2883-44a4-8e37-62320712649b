{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'embed_dim': 50, 'hidden_size': 32, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False}
[Aug-03-2025_11-48-18] - 开始训练，配置参数如下：
[Aug-03-2025_11-48-18] - early_stop_enabled: True
[Aug-03-2025_11-48-18] - model: FMLP
[Aug-03-2025_11-48-18] - lr: 0.001
[Aug-03-2025_11-48-18] - batch_size: 128
[Aug-03-2025_11-48-18] - neg_num: 99
[Aug-03-2025_11-48-18] - l2_reg: 0
[Aug-03-2025_11-48-18] - l2_emb: 0.0
[Aug-03-2025_11-48-18] - embed_dim: 50
[Aug-03-2025_11-48-18] - hidden_size: 32
[Aug-03-2025_11-48-18] - dropout: 0.2
[Aug-03-2025_11-48-18] - epochs: 1000000
[Aug-03-2025_11-48-18] - early_stop: 50
[Aug-03-2025_11-48-18] - datapath: ../../data/
[Aug-03-2025_11-48-18] - dataset: ml-100k
[Aug-03-2025_11-48-18] - train_data: ml-100k.txt
[Aug-03-2025_11-48-18] - log_path: ../log
[Aug-03-2025_11-48-18] - num_layers: 2
[Aug-03-2025_11-48-18] - num_heads: 1
[Aug-03-2025_11-48-18] - inner_size: 256
[Aug-03-2025_11-48-18] - max_seq_len: 200
[Aug-03-2025_11-48-18] - upload_mode: full
[Aug-03-2025_11-48-18] - skip_test_eval: True
[Aug-03-2025_11-48-18] - eval_freq: 1
[Aug-03-2025_11-48-18] - use_dynamic_sampling: False
[Aug-03-2025_11-48-18] - norm_first: False
[Aug-03-2025_11-48-18] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-03-2025_11-48-18] - 最大序列长度: 200
[Aug-03-2025_11-48-18] - 批次大小: 128
[Aug-03-2025_11-48-20] - 参数上传模式: full
[Aug-03-2025_11-48-20] - 隐私参数（本地更新）: []
[Aug-03-2025_11-48-20] - 非隐私参数（服务器聚合）: ['item_embeddings.weight', 'position_embeddings.weight', 'LayerNorm.weight', 'LayerNorm.bias', 'item_encoder.layer.0.filterlayer.complex_weight', 'item_encoder.layer.0.filterlayer.LayerNorm.weight', 'item_encoder.layer.0.filterlayer.LayerNorm.bias', 'item_encoder.layer.0.intermediate.dense_1.weight', 'item_encoder.layer.0.intermediate.dense_1.bias', 'item_encoder.layer.0.intermediate.dense_2.weight', 'item_encoder.layer.0.intermediate.dense_2.bias', 'item_encoder.layer.0.intermediate.LayerNorm.weight', 'item_encoder.layer.0.intermediate.LayerNorm.bias', 'item_encoder.layer.1.filterlayer.complex_weight', 'item_encoder.layer.1.filterlayer.LayerNorm.weight', 'item_encoder.layer.1.filterlayer.LayerNorm.bias', 'item_encoder.layer.1.intermediate.dense_1.weight', 'item_encoder.layer.1.intermediate.dense_1.bias', 'item_encoder.layer.1.intermediate.dense_2.weight', 'item_encoder.layer.1.intermediate.dense_2.bias', 'item_encoder.layer.1.intermediate.LayerNorm.weight', 'item_encoder.layer.1.intermediate.LayerNorm.bias']
[Aug-03-2025_11-48-20] - 动态负采样: False
[Aug-03-2025_11-48-20] - 用户数量: 943
[Aug-03-2025_11-48-20] - 物品数量: 1349
