{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'embed_dim': 50, 'hidden_size': 32, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False}
[Aug-02-2025_21-24-28] - 开始训练，配置参数如下：
[Aug-02-2025_21-24-28] - early_stop_enabled: True
[Aug-02-2025_21-24-28] - model: FMLP
[Aug-02-2025_21-24-28] - lr: 0.001
[Aug-02-2025_21-24-28] - batch_size: 128
[Aug-02-2025_21-24-28] - neg_num: 99
[Aug-02-2025_21-24-28] - l2_reg: 0
[Aug-02-2025_21-24-28] - l2_emb: 0.0
[Aug-02-2025_21-24-28] - embed_dim: 50
[Aug-02-2025_21-24-28] - hidden_size: 32
[Aug-02-2025_21-24-28] - dropout: 0.2
[Aug-02-2025_21-24-28] - epochs: 1000000
[Aug-02-2025_21-24-28] - early_stop: 50
[Aug-02-2025_21-24-28] - datapath: ../../data/
[Aug-02-2025_21-24-28] - dataset: ml-100k
[Aug-02-2025_21-24-28] - train_data: ml-100k.txt
[Aug-02-2025_21-24-28] - log_path: ../log
[Aug-02-2025_21-24-28] - num_layers: 2
[Aug-02-2025_21-24-28] - num_heads: 1
[Aug-02-2025_21-24-28] - inner_size: 256
[Aug-02-2025_21-24-28] - max_seq_len: 200
[Aug-02-2025_21-24-28] - upload_mode: full
[Aug-02-2025_21-24-28] - skip_test_eval: True
[Aug-02-2025_21-24-28] - eval_freq: 1
[Aug-02-2025_21-24-28] - use_dynamic_sampling: False
[Aug-02-2025_21-24-28] - norm_first: False
[Aug-02-2025_21-24-28] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-02-2025_21-24-28] - 最大序列长度: 200
[Aug-02-2025_21-24-28] - 批次大小: 128
[Aug-02-2025_21-24-30] - 参数上传模式: full
[Aug-02-2025_21-24-30] - 隐私参数（本地更新）: []
[Aug-02-2025_21-24-30] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'LayerNorm.weight', 'LayerNorm.bias', 'encoder.layer.0.filter_layer.complex_weight', 'encoder.layer.0.feed_forward.dense_1.weight', 'encoder.layer.0.feed_forward.dense_1.bias', 'encoder.layer.0.feed_forward.dense_2.weight', 'encoder.layer.0.feed_forward.dense_2.bias', 'encoder.layer.0.first_layer_norm.weight', 'encoder.layer.0.first_layer_norm.bias', 'encoder.layer.0.second_layer_norm.weight', 'encoder.layer.0.second_layer_norm.bias', 'encoder.layer.1.filter_layer.complex_weight', 'encoder.layer.1.feed_forward.dense_1.weight', 'encoder.layer.1.feed_forward.dense_1.bias', 'encoder.layer.1.feed_forward.dense_2.weight', 'encoder.layer.1.feed_forward.dense_2.bias', 'encoder.layer.1.first_layer_norm.weight', 'encoder.layer.1.first_layer_norm.bias', 'encoder.layer.1.second_layer_norm.weight', 'encoder.layer.1.second_layer_norm.bias']
[Aug-02-2025_21-24-30] - 动态负采样: False
[Aug-02-2025_21-24-30] - 用户数量: 943
[Aug-02-2025_21-24-30] - 物品数量: 1349
[Aug-02-2025_21-24-46] - epoch:1, time: 10.440212(s), valid (NDCG@10: 0.0531, HR@10: 0.1177), test: SKIPPED, all_time: 10.440212(s)
[Aug-02-2025_21-24-46] - 新的最佳性能: valid NDCG@10=0.0531, valid HR@10=0.1177
[Aug-02-2025_21-25-02] - epoch:2, time: 11.471906(s), valid (NDCG@10: 0.0737, HR@10: 0.1527), test: SKIPPED, all_time: 21.912118(s)
[Aug-02-2025_21-25-02] - 新的最佳性能: valid NDCG@10=0.0737, valid HR@10=0.1527
[Aug-02-2025_21-25-19] - epoch:3, time: 11.782529(s), valid (NDCG@10: 0.1088, HR@10: 0.2301), test: SKIPPED, all_time: 33.694647(s)
[Aug-02-2025_21-25-19] - 新的最佳性能: valid NDCG@10=0.1088, valid HR@10=0.2301
[Aug-02-2025_21-25-34] - epoch:4, time: 10.500159(s), valid (NDCG@10: 0.1427, HR@10: 0.2927), test: SKIPPED, all_time: 44.194806(s)
[Aug-02-2025_21-25-34] - 新的最佳性能: valid NDCG@10=0.1427, valid HR@10=0.2927
[Aug-02-2025_21-25-50] - epoch:5, time: 11.189309(s), valid (NDCG@10: 0.1823, HR@10: 0.3510), test: SKIPPED, all_time: 55.384115(s)
[Aug-02-2025_21-25-50] - 新的最佳性能: valid NDCG@10=0.1823, valid HR@10=0.3510
[Aug-02-2025_21-26-05] - epoch:6, time: 11.060449(s), valid (NDCG@10: 0.2060, HR@10: 0.3871), test: SKIPPED, all_time: 66.444563(s)
[Aug-02-2025_21-26-05] - 新的最佳性能: valid NDCG@10=0.2060, valid HR@10=0.3871
[Aug-02-2025_21-26-21] - epoch:7, time: 10.681449(s), valid (NDCG@10: 0.2315, HR@10: 0.4199), test: SKIPPED, all_time: 77.126012(s)
[Aug-02-2025_21-26-21] - 新的最佳性能: valid NDCG@10=0.2315, valid HR@10=0.4199
[Aug-02-2025_21-26-36] - epoch:8, time: 11.144352(s), valid (NDCG@10: 0.2434, HR@10: 0.4422), test: SKIPPED, all_time: 88.270365(s)
[Aug-02-2025_21-26-36] - 新的最佳性能: valid NDCG@10=0.2434, valid HR@10=0.4422
[Aug-02-2025_21-26-53] - epoch:9, time: 12.027040(s), valid (NDCG@10: 0.2612, HR@10: 0.4655), test: SKIPPED, all_time: 100.297405(s)
[Aug-02-2025_21-26-53] - 新的最佳性能: valid NDCG@10=0.2612, valid HR@10=0.4655
[Aug-02-2025_21-27-10] - epoch:10, time: 12.277729(s), valid (NDCG@10: 0.2798, HR@10: 0.4963), test: SKIPPED, all_time: 112.575134(s)
[Aug-02-2025_21-27-10] - 新的最佳性能: valid NDCG@10=0.2798, valid HR@10=0.4963
[Aug-02-2025_21-27-25] - epoch:11, time: 10.479038(s), valid (NDCG@10: 0.2829, HR@10: 0.4984), test: SKIPPED, all_time: 123.054172(s)
[Aug-02-2025_21-27-25] - 新的最佳性能: valid NDCG@10=0.2829, valid HR@10=0.4984
[Aug-02-2025_21-27-40] - epoch:12, time: 10.658227(s), valid (NDCG@10: 0.2962, HR@10: 0.5228), test: SKIPPED, all_time: 133.712399(s)
[Aug-02-2025_21-27-40] - 新的最佳性能: valid NDCG@10=0.2962, valid HR@10=0.5228
[Aug-02-2025_21-27-55] - epoch:13, time: 11.085532(s), valid (NDCG@10: 0.3055, HR@10: 0.5292), test: SKIPPED, all_time: 144.797931(s)
[Aug-02-2025_21-27-55] - 新的最佳性能: valid NDCG@10=0.3055, valid HR@10=0.5292
[Aug-02-2025_21-28-10] - epoch:14, time: 10.643291(s), valid (NDCG@10: 0.3082, HR@10: 0.5302), test: SKIPPED, all_time: 155.441221(s)
[Aug-02-2025_21-28-10] - 新的最佳性能: valid NDCG@10=0.3082, valid HR@10=0.5302
[Aug-02-2025_21-28-26] - epoch:15, time: 11.265347(s), valid (NDCG@10: 0.3047, HR@10: 0.5366), test: SKIPPED, all_time: 166.706568(s)
[Aug-02-2025_21-28-26] - 新的最佳性能: valid NDCG@10=0.3082, valid HR@10=0.5366
[Aug-02-2025_21-28-42] - epoch:16, time: 12.409357(s), valid (NDCG@10: 0.3186, HR@10: 0.5408), test: SKIPPED, all_time: 179.115925(s)
[Aug-02-2025_21-28-42] - 新的最佳性能: valid NDCG@10=0.3186, valid HR@10=0.5408
[Aug-02-2025_21-28-58] - epoch:17, time: 11.103581(s), valid (NDCG@10: 0.3169, HR@10: 0.5483), test: SKIPPED, all_time: 190.219506(s)
[Aug-02-2025_21-28-58] - 新的最佳性能: valid NDCG@10=0.3186, valid HR@10=0.5483
[Aug-02-2025_21-29-14] - epoch:18, time: 11.344734(s), valid (NDCG@10: 0.3213, HR@10: 0.5546), test: SKIPPED, all_time: 201.564240(s)
[Aug-02-2025_21-29-14] - 新的最佳性能: valid NDCG@10=0.3213, valid HR@10=0.5546
[Aug-02-2025_21-29-29] - epoch:19, time: 11.207006(s), valid (NDCG@10: 0.3274, HR@10: 0.5673), test: SKIPPED, all_time: 212.771246(s)
[Aug-02-2025_21-29-29] - 新的最佳性能: valid NDCG@10=0.3274, valid HR@10=0.5673
[Aug-02-2025_21-29-43] - epoch:20, time: 10.294795(s), valid (NDCG@10: 0.3357, HR@10: 0.5631), test: SKIPPED, all_time: 223.066041(s)
[Aug-02-2025_21-29-57] - epoch:21, time: 10.237670(s), valid (NDCG@10: 0.3332, HR@10: 0.5610), test: SKIPPED, all_time: 233.303711(s)
[Aug-02-2025_21-30-12] - epoch:22, time: 10.390496(s), valid (NDCG@10: 0.3279, HR@10: 0.5684), test: SKIPPED, all_time: 243.694207(s)
[Aug-02-2025_21-30-12] - 新的最佳性能: valid NDCG@10=0.3357, valid HR@10=0.5684
[Aug-02-2025_21-30-28] - epoch:23, time: 11.431960(s), valid (NDCG@10: 0.3268, HR@10: 0.5726), test: SKIPPED, all_time: 255.126167(s)
[Aug-02-2025_21-30-28] - 新的最佳性能: valid NDCG@10=0.3357, valid HR@10=0.5726
[Aug-02-2025_21-30-44] - epoch:24, time: 11.740825(s), valid (NDCG@10: 0.3333, HR@10: 0.5684), test: SKIPPED, all_time: 266.866992(s)
[Aug-02-2025_21-31-00] - epoch:25, time: 11.636444(s), valid (NDCG@10: 0.3383, HR@10: 0.5684), test: SKIPPED, all_time: 278.503436(s)
[Aug-02-2025_21-31-15] - epoch:26, time: 11.040139(s), valid (NDCG@10: 0.3363, HR@10: 0.5716), test: SKIPPED, all_time: 289.543575(s)
[Aug-02-2025_21-31-31] - epoch:27, time: 11.248044(s), valid (NDCG@10: 0.3352, HR@10: 0.5673), test: SKIPPED, all_time: 300.791619(s)
[Aug-02-2025_21-31-47] - epoch:28, time: 10.657051(s), valid (NDCG@10: 0.3440, HR@10: 0.5864), test: SKIPPED, all_time: 311.448670(s)
[Aug-02-2025_21-31-47] - 新的最佳性能: valid NDCG@10=0.3440, valid HR@10=0.5864
[Aug-02-2025_21-32-09] - epoch:29, time: 11.921424(s), valid (NDCG@10: 0.3306, HR@10: 0.5705), test: SKIPPED, all_time: 323.370094(s)
[Aug-02-2025_21-32-26] - epoch:30, time: 12.298344(s), valid (NDCG@10: 0.3404, HR@10: 0.5790), test: SKIPPED, all_time: 335.668439(s)
[Aug-02-2025_21-32-43] - epoch:31, time: 11.629659(s), valid (NDCG@10: 0.3488, HR@10: 0.5885), test: SKIPPED, all_time: 347.298098(s)
[Aug-02-2025_21-32-43] - 新的最佳性能: valid NDCG@10=0.3488, valid HR@10=0.5885
[Aug-02-2025_21-33-00] - epoch:32, time: 12.170103(s), valid (NDCG@10: 0.3404, HR@10: 0.5854), test: SKIPPED, all_time: 359.468201(s)
[Aug-02-2025_21-33-17] - epoch:33, time: 13.006531(s), valid (NDCG@10: 0.3354, HR@10: 0.5737), test: SKIPPED, all_time: 372.474732(s)
[Aug-02-2025_21-33-33] - epoch:34, time: 11.198004(s), valid (NDCG@10: 0.3327, HR@10: 0.5695), test: SKIPPED, all_time: 383.672736(s)
[Aug-02-2025_21-33-48] - epoch:35, time: 11.253522(s), valid (NDCG@10: 0.3377, HR@10: 0.5737), test: SKIPPED, all_time: 394.926258(s)
[Aug-02-2025_21-34-04] - epoch:36, time: 10.770716(s), valid (NDCG@10: 0.3385, HR@10: 0.5822), test: SKIPPED, all_time: 405.696974(s)
[Aug-02-2025_21-34-20] - epoch:37, time: 11.343773(s), valid (NDCG@10: 0.3361, HR@10: 0.5811), test: SKIPPED, all_time: 417.040747(s)
[Aug-02-2025_21-34-36] - epoch:38, time: 11.510182(s), valid (NDCG@10: 0.3440, HR@10: 0.5832), test: SKIPPED, all_time: 428.550930(s)
[Aug-02-2025_21-34-52] - epoch:39, time: 11.688284(s), valid (NDCG@10: 0.3373, HR@10: 0.5684), test: SKIPPED, all_time: 440.239214(s)
[Aug-02-2025_21-35-09] - epoch:40, time: 12.388378(s), valid (NDCG@10: 0.3394, HR@10: 0.5854), test: SKIPPED, all_time: 452.627592(s)
[Aug-02-2025_21-35-24] - epoch:41, time: 11.069604(s), valid (NDCG@10: 0.3415, HR@10: 0.5811), test: SKIPPED, all_time: 463.697196(s)
[Aug-02-2025_21-35-41] - epoch:42, time: 11.546987(s), valid (NDCG@10: 0.3378, HR@10: 0.5758), test: SKIPPED, all_time: 475.244183(s)
[Aug-02-2025_21-35-58] - epoch:43, time: 12.734656(s), valid (NDCG@10: 0.3444, HR@10: 0.5705), test: SKIPPED, all_time: 487.978839(s)
[Aug-02-2025_21-36-13] - epoch:44, time: 10.992214(s), valid (NDCG@10: 0.3389, HR@10: 0.5748), test: SKIPPED, all_time: 498.971053(s)
[Aug-02-2025_21-36-30] - epoch:45, time: 11.970064(s), valid (NDCG@10: 0.3375, HR@10: 0.5726), test: SKIPPED, all_time: 510.941117(s)
[Aug-02-2025_21-36-44] - epoch:46, time: 10.530115(s), valid (NDCG@10: 0.3456, HR@10: 0.5748), test: SKIPPED, all_time: 521.471232(s)
[Aug-02-2025_21-37-00] - epoch:47, time: 11.386261(s), valid (NDCG@10: 0.3364, HR@10: 0.5758), test: SKIPPED, all_time: 532.857493(s)
[Aug-02-2025_21-37-16] - epoch:48, time: 10.360861(s), valid (NDCG@10: 0.3429, HR@10: 0.5801), test: SKIPPED, all_time: 543.218354(s)
[Aug-02-2025_21-37-31] - epoch:49, time: 11.165364(s), valid (NDCG@10: 0.3433, HR@10: 0.5769), test: SKIPPED, all_time: 554.383718(s)
[Aug-02-2025_21-37-46] - epoch:50, time: 10.329896(s), valid (NDCG@10: 0.3533, HR@10: 0.5854), test: SKIPPED, all_time: 564.713614(s)
[Aug-02-2025_21-38-03] - epoch:51, time: 11.345306(s), valid (NDCG@10: 0.3434, HR@10: 0.5832), test: SKIPPED, all_time: 576.058920(s)
[Aug-02-2025_21-38-20] - epoch:52, time: 12.027187(s), valid (NDCG@10: 0.3397, HR@10: 0.5822), test: SKIPPED, all_time: 588.086107(s)
[Aug-02-2025_21-38-36] - epoch:53, time: 11.738241(s), valid (NDCG@10: 0.3392, HR@10: 0.5811), test: SKIPPED, all_time: 599.824349(s)
[Aug-02-2025_21-38-50] - epoch:54, time: 10.543920(s), valid (NDCG@10: 0.3459, HR@10: 0.5758), test: SKIPPED, all_time: 610.368269(s)
[Aug-02-2025_21-39-07] - epoch:55, time: 11.705692(s), valid (NDCG@10: 0.3431, HR@10: 0.5843), test: SKIPPED, all_time: 622.073960(s)
[Aug-02-2025_21-39-25] - epoch:56, time: 12.956689(s), valid (NDCG@10: 0.3507, HR@10: 0.5737), test: SKIPPED, all_time: 635.030649(s)
[Aug-02-2025_21-39-42] - epoch:57, time: 12.513457(s), valid (NDCG@10: 0.3387, HR@10: 0.5705), test: SKIPPED, all_time: 647.544106(s)
[Aug-02-2025_21-40-01] - epoch:58, time: 13.109989(s), valid (NDCG@10: 0.3414, HR@10: 0.5610), test: SKIPPED, all_time: 660.654096(s)
[Aug-02-2025_21-40-16] - epoch:59, time: 10.746642(s), valid (NDCG@10: 0.3442, HR@10: 0.5885), test: SKIPPED, all_time: 671.400738(s)
[Aug-02-2025_21-40-31] - epoch:60, time: 9.610394(s), valid (NDCG@10: 0.3454, HR@10: 0.5769), test: SKIPPED, all_time: 681.011131(s)
[Aug-02-2025_21-40-48] - epoch:61, time: 13.337659(s), valid (NDCG@10: 0.3365, HR@10: 0.5748), test: SKIPPED, all_time: 694.348790(s)
[Aug-02-2025_21-41-05] - epoch:62, time: 12.439780(s), valid (NDCG@10: 0.3436, HR@10: 0.5811), test: SKIPPED, all_time: 706.788570(s)
[Aug-02-2025_21-41-20] - epoch:63, time: 10.721723(s), valid (NDCG@10: 0.3547, HR@10: 0.5832), test: SKIPPED, all_time: 717.510293(s)
[Aug-02-2025_21-41-36] - epoch:64, time: 11.177016(s), valid (NDCG@10: 0.3385, HR@10: 0.5801), test: SKIPPED, all_time: 728.687309(s)
[Aug-02-2025_21-41-52] - epoch:65, time: 11.284096(s), valid (NDCG@10: 0.3395, HR@10: 0.5748), test: SKIPPED, all_time: 739.971405(s)
[Aug-02-2025_21-42-07] - epoch:66, time: 11.044404(s), valid (NDCG@10: 0.3392, HR@10: 0.5779), test: SKIPPED, all_time: 751.015809(s)
[Aug-02-2025_21-42-23] - epoch:67, time: 10.671825(s), valid (NDCG@10: 0.3380, HR@10: 0.5769), test: SKIPPED, all_time: 761.687634(s)
[Aug-02-2025_21-42-39] - epoch:68, time: 10.953650(s), valid (NDCG@10: 0.3441, HR@10: 0.5790), test: SKIPPED, all_time: 772.641284(s)
[Aug-02-2025_21-42-55] - epoch:69, time: 11.551039(s), valid (NDCG@10: 0.3392, HR@10: 0.5737), test: SKIPPED, all_time: 784.192323(s)
[Aug-02-2025_21-43-10] - epoch:70, time: 10.624527(s), valid (NDCG@10: 0.3348, HR@10: 0.5663), test: SKIPPED, all_time: 794.816850(s)
[Aug-02-2025_21-43-26] - epoch:71, time: 12.155801(s), valid (NDCG@10: 0.3399, HR@10: 0.5695), test: SKIPPED, all_time: 806.972651(s)
[Aug-02-2025_21-43-41] - epoch:72, time: 10.832722(s), valid (NDCG@10: 0.3443, HR@10: 0.5705), test: SKIPPED, all_time: 817.805373(s)
[Aug-02-2025_21-43-56] - epoch:73, time: 10.864800(s), valid (NDCG@10: 0.3402, HR@10: 0.5769), test: SKIPPED, all_time: 828.670174(s)
[Aug-02-2025_21-44-12] - epoch:74, time: 11.438463(s), valid (NDCG@10: 0.3411, HR@10: 0.5758), test: SKIPPED, all_time: 840.108637(s)
[Aug-02-2025_21-44-28] - epoch:75, time: 11.824964(s), valid (NDCG@10: 0.3324, HR@10: 0.5663), test: SKIPPED, all_time: 851.933601(s)
[Aug-02-2025_21-44-46] - epoch:76, time: 11.339652(s), valid (NDCG@10: 0.3414, HR@10: 0.5769), test: SKIPPED, all_time: 863.273253(s)
[Aug-02-2025_21-45-06] - epoch:77, time: 14.766466(s), valid (NDCG@10: 0.3341, HR@10: 0.5610), test: SKIPPED, all_time: 878.039719(s)
[Aug-02-2025_21-45-24] - epoch:78, time: 12.543820(s), valid (NDCG@10: 0.3434, HR@10: 0.5695), test: SKIPPED, all_time: 890.583539(s)
[Aug-02-2025_21-45-50] - epoch:79, time: 20.939917(s), valid (NDCG@10: 0.3390, HR@10: 0.5748), test: SKIPPED, all_time: 911.523456(s)
[Aug-02-2025_21-46-08] - epoch:80, time: 12.640056(s), valid (NDCG@10: 0.3353, HR@10: 0.5748), test: SKIPPED, all_time: 924.163512(s)
[Aug-02-2025_21-46-24] - epoch:81, time: 11.930600(s), valid (NDCG@10: 0.3328, HR@10: 0.5610), test: SKIPPED, all_time: 936.094112(s)
[Aug-02-2025_21-46-39] - epoch:82, time: 11.129315(s), valid (NDCG@10: 0.3428, HR@10: 0.5843), test: SKIPPED, all_time: 947.223427(s)
[Aug-02-2025_21-46-54] - epoch:83, time: 10.679553(s), valid (NDCG@10: 0.3406, HR@10: 0.5684), test: SKIPPED, all_time: 957.902979(s)
[Aug-02-2025_21-47-10] - epoch:84, time: 11.471912(s), valid (NDCG@10: 0.3453, HR@10: 0.5854), test: SKIPPED, all_time: 969.374892(s)
[Aug-02-2025_21-47-26] - epoch:85, time: 11.099653(s), valid (NDCG@10: 0.3398, HR@10: 0.5758), test: SKIPPED, all_time: 980.474545(s)
[Aug-02-2025_21-47-42] - epoch:86, time: 12.457281(s), valid (NDCG@10: 0.3342, HR@10: 0.5758), test: SKIPPED, all_time: 992.931826(s)
[Aug-02-2025_21-47-57] - epoch:87, time: 10.583123(s), valid (NDCG@10: 0.3336, HR@10: 0.5769), test: SKIPPED, all_time: 1003.514949(s)
[Aug-02-2025_21-48-12] - epoch:88, time: 10.545540(s), valid (NDCG@10: 0.3421, HR@10: 0.5748), test: SKIPPED, all_time: 1014.060489(s)
[Aug-02-2025_21-48-28] - epoch:89, time: 10.406966(s), valid (NDCG@10: 0.3387, HR@10: 0.5705), test: SKIPPED, all_time: 1024.467456(s)
[Aug-02-2025_21-48-44] - epoch:90, time: 12.111687(s), valid (NDCG@10: 0.3312, HR@10: 0.5737), test: SKIPPED, all_time: 1036.579143(s)
[Aug-02-2025_21-48-59] - epoch:91, time: 10.424228(s), valid (NDCG@10: 0.3357, HR@10: 0.5705), test: SKIPPED, all_time: 1047.003371(s)
[Aug-02-2025_21-49-14] - epoch:92, time: 11.513792(s), valid (NDCG@10: 0.3370, HR@10: 0.5684), test: SKIPPED, all_time: 1058.517163(s)
[Aug-02-2025_21-49-29] - epoch:93, time: 10.436516(s), valid (NDCG@10: 0.3352, HR@10: 0.5811), test: SKIPPED, all_time: 1068.953679(s)
[Aug-02-2025_21-49-43] - epoch:94, time: 10.251046(s), valid (NDCG@10: 0.3284, HR@10: 0.5589), test: SKIPPED, all_time: 1079.204725(s)
[Aug-02-2025_21-49-58] - epoch:95, time: 10.764742(s), valid (NDCG@10: 0.3365, HR@10: 0.5673), test: SKIPPED, all_time: 1089.969467(s)
[Aug-02-2025_21-50-13] - epoch:96, time: 11.126910(s), valid (NDCG@10: 0.3393, HR@10: 0.5705), test: SKIPPED, all_time: 1101.096377(s)
[Aug-02-2025_21-50-31] - epoch:97, time: 12.299318(s), valid (NDCG@10: 0.3401, HR@10: 0.5779), test: SKIPPED, all_time: 1113.395695(s)
[Aug-02-2025_21-50-47] - epoch:98, time: 11.137351(s), valid (NDCG@10: 0.3286, HR@10: 0.5631), test: SKIPPED, all_time: 1124.533046(s)
[Aug-02-2025_21-51-03] - epoch:99, time: 11.371850(s), valid (NDCG@10: 0.3402, HR@10: 0.5748), test: SKIPPED, all_time: 1135.904897(s)
[Aug-02-2025_21-51-21] - epoch:100, time: 13.085625(s), valid (NDCG@10: 0.3340, HR@10: 0.5758), test: SKIPPED, all_time: 1148.990522(s)
[Aug-02-2025_21-51-38] - epoch:101, time: 12.228497(s), valid (NDCG@10: 0.3363, HR@10: 0.5673), test: SKIPPED, all_time: 1161.219019(s)
[Aug-02-2025_21-51-54] - epoch:102, time: 12.117112(s), valid (NDCG@10: 0.3392, HR@10: 0.5589), test: SKIPPED, all_time: 1173.336131(s)
[Aug-02-2025_21-52-11] - epoch:103, time: 11.852771(s), valid (NDCG@10: 0.3386, HR@10: 0.5737), test: SKIPPED, all_time: 1185.188902(s)
[Aug-02-2025_21-52-28] - epoch:104, time: 12.121338(s), valid (NDCG@10: 0.3339, HR@10: 0.5758), test: SKIPPED, all_time: 1197.310240(s)
[Aug-02-2025_21-52-44] - epoch:105, time: 11.603832(s), valid (NDCG@10: 0.3409, HR@10: 0.5801), test: SKIPPED, all_time: 1208.914072(s)
[Aug-02-2025_21-53-00] - epoch:106, time: 10.580225(s), valid (NDCG@10: 0.3240, HR@10: 0.5684), test: SKIPPED, all_time: 1219.494297(s)
[Aug-02-2025_21-53-19] - epoch:107, time: 12.037605(s), valid (NDCG@10: 0.3369, HR@10: 0.5673), test: SKIPPED, all_time: 1231.531901(s)
[Aug-02-2025_21-53-37] - epoch:108, time: 13.685811(s), valid (NDCG@10: 0.3347, HR@10: 0.5769), test: SKIPPED, all_time: 1245.217713(s)
[Aug-02-2025_21-53-52] - epoch:109, time: 10.862895(s), valid (NDCG@10: 0.3319, HR@10: 0.5663), test: SKIPPED, all_time: 1256.080608(s)
[Aug-02-2025_21-54-09] - epoch:110, time: 12.722867(s), valid (NDCG@10: 0.3363, HR@10: 0.5673), test: SKIPPED, all_time: 1268.803475(s)
[Aug-02-2025_21-54-25] - epoch:111, time: 11.212889(s), valid (NDCG@10: 0.3287, HR@10: 0.5589), test: SKIPPED, all_time: 1280.016364(s)
[Aug-02-2025_21-54-42] - epoch:112, time: 10.370835(s), valid (NDCG@10: 0.3329, HR@10: 0.5663), test: SKIPPED, all_time: 1290.387198(s)
[Aug-02-2025_21-54-57] - 早停触发！NDCG在50轮内没有改善。
[Aug-02-2025_21-54-57] - epoch:113, time: 10.550035(s), valid (NDCG@10: 0.3353, HR@10: 0.5737), test: SKIPPED, all_time: 1300.937233(s)
[Aug-02-2025_21-54-57] - [联邦训练] 最佳结果: valid NDCG@10=0.3547, HR@10=0.5885 (测试集评估已跳过)
