{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 100, 'datapath': '../../data/', 'dataset': 'LastFM', 'train_data': 'LastFM.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False, 'c': 9, 'alpha': 0.3}
[Aug-05-2025_13-06-07] - 开始训练，配置参数如下：
[Aug-05-2025_13-06-07] - early_stop_enabled: True
[Aug-05-2025_13-06-07] - model: FMLP
[Aug-05-2025_13-06-07] - lr: 0.001
[Aug-05-2025_13-06-07] - batch_size: 128
[Aug-05-2025_13-06-07] - neg_num: 99
[Aug-05-2025_13-06-07] - l2_reg: 0
[Aug-05-2025_13-06-07] - l2_emb: 0.0
[Aug-05-2025_13-06-07] - hidden_size: 50
[Aug-05-2025_13-06-07] - dropout: 0.2
[Aug-05-2025_13-06-07] - epochs: 1000000
[Aug-05-2025_13-06-07] - early_stop: 100
[Aug-05-2025_13-06-07] - datapath: ../../data/
[Aug-05-2025_13-06-07] - dataset: LastFM
[Aug-05-2025_13-06-07] - train_data: LastFM.txt
[Aug-05-2025_13-06-07] - log_path: ../log
[Aug-05-2025_13-06-07] - num_layers: 2
[Aug-05-2025_13-06-07] - num_heads: 1
[Aug-05-2025_13-06-07] - inner_size: 256
[Aug-05-2025_13-06-07] - max_seq_len: 200
[Aug-05-2025_13-06-07] - upload_mode: full
[Aug-05-2025_13-06-07] - skip_test_eval: True
[Aug-05-2025_13-06-07] - eval_freq: 1
[Aug-05-2025_13-06-07] - use_dynamic_sampling: False
[Aug-05-2025_13-06-07] - norm_first: False
[Aug-05-2025_13-06-07] - c: 9
[Aug-05-2025_13-06-07] - alpha: 0.3
[Aug-05-2025_13-06-07] - 训练数据: ../../data/LastFM/LastFM.txt
[Aug-05-2025_13-06-07] - 最大序列长度: 200
[Aug-05-2025_13-06-07] - 批次大小: 128
[Aug-05-2025_13-06-10] - 参数上传模式: full
[Aug-05-2025_13-06-10] - 隐私参数（本地更新）: []
[Aug-05-2025_13-06-10] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'trm_encoder.layer.0.filter_layer.complex_weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.bias', 'trm_encoder.layer.0.feed_forward.dense_1.weight', 'trm_encoder.layer.0.feed_forward.dense_1.bias', 'trm_encoder.layer.0.feed_forward.dense_2.weight', 'trm_encoder.layer.0.feed_forward.dense_2.bias', 'trm_encoder.layer.0.feed_forward.LayerNorm.weight', 'trm_encoder.layer.0.feed_forward.LayerNorm.bias', 'trm_encoder.layer.1.filter_layer.complex_weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.bias', 'trm_encoder.layer.1.feed_forward.dense_1.weight', 'trm_encoder.layer.1.feed_forward.dense_1.bias', 'trm_encoder.layer.1.feed_forward.dense_2.weight', 'trm_encoder.layer.1.feed_forward.dense_2.bias', 'trm_encoder.layer.1.feed_forward.LayerNorm.weight', 'trm_encoder.layer.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-05-2025_13-06-10] - 动态负采样: False
[Aug-05-2025_13-06-10] - 用户数量: 1090
[Aug-05-2025_13-06-10] - 物品数量: 3646
[Aug-05-2025_13-06-34] - epoch:1, time: 24.339967(s), valid (NDCG@10: 0.0814, HR@10: 0.1661), test: SKIPPED, all_time: 24.339967(s)
[Aug-05-2025_13-06-34] - 新的最佳性能: valid NDCG@10=0.0814, valid HR@10=0.1661
[Aug-05-2025_13-06-55] - epoch:2, time: 20.266758(s), valid (NDCG@10: 0.1390, HR@10: 0.2697), test: SKIPPED, all_time: 44.606725(s)
[Aug-05-2025_13-06-55] - 新的最佳性能: valid NDCG@10=0.1390, valid HR@10=0.2697
[Aug-05-2025_13-07-14] - epoch:3, time: 19.584031(s), valid (NDCG@10: 0.1676, HR@10: 0.3092), test: SKIPPED, all_time: 64.190756(s)
[Aug-05-2025_13-07-14] - 新的最佳性能: valid NDCG@10=0.1676, valid HR@10=0.3092
[Aug-05-2025_13-07-34] - epoch:4, time: 19.775957(s), valid (NDCG@10: 0.1718, HR@10: 0.3138), test: SKIPPED, all_time: 83.966713(s)
[Aug-05-2025_13-07-34] - 新的最佳性能: valid NDCG@10=0.1718, valid HR@10=0.3138
[Aug-05-2025_13-07-53] - epoch:5, time: 19.076903(s), valid (NDCG@10: 0.1539, HR@10: 0.2798), test: SKIPPED, all_time: 103.043616(s)
[Aug-05-2025_13-08-12] - epoch:6, time: 18.682662(s), valid (NDCG@10: 0.1504, HR@10: 0.2798), test: SKIPPED, all_time: 121.726278(s)
[Aug-05-2025_13-08-31] - epoch:7, time: 19.322408(s), valid (NDCG@10: 0.1517, HR@10: 0.2706), test: SKIPPED, all_time: 141.048686(s)
[Aug-05-2025_13-08-51] - epoch:8, time: 19.939022(s), valid (NDCG@10: 0.1451, HR@10: 0.2615), test: SKIPPED, all_time: 160.987707(s)
[Aug-05-2025_13-09-10] - epoch:9, time: 18.779815(s), valid (NDCG@10: 0.1443, HR@10: 0.2752), test: SKIPPED, all_time: 179.767522(s)
[Aug-05-2025_13-09-28] - epoch:10, time: 18.406832(s), valid (NDCG@10: 0.1500, HR@10: 0.2743), test: SKIPPED, all_time: 198.174355(s)
[Aug-05-2025_13-09-48] - epoch:11, time: 19.289520(s), valid (NDCG@10: 0.1517, HR@10: 0.2725), test: SKIPPED, all_time: 217.463874(s)
[Aug-05-2025_13-10-07] - epoch:12, time: 19.005189(s), valid (NDCG@10: 0.1552, HR@10: 0.2862), test: SKIPPED, all_time: 236.469063(s)
[Aug-05-2025_13-10-25] - epoch:13, time: 18.837677(s), valid (NDCG@10: 0.1507, HR@10: 0.2725), test: SKIPPED, all_time: 255.306740(s)
[Aug-05-2025_13-10-44] - epoch:14, time: 18.567916(s), valid (NDCG@10: 0.1540, HR@10: 0.2798), test: SKIPPED, all_time: 273.874656(s)
[Aug-05-2025_13-11-03] - epoch:15, time: 18.842068(s), valid (NDCG@10: 0.1543, HR@10: 0.2872), test: SKIPPED, all_time: 292.716724(s)
[Aug-05-2025_13-11-21] - epoch:16, time: 18.672251(s), valid (NDCG@10: 0.1558, HR@10: 0.2881), test: SKIPPED, all_time: 311.388975(s)
[Aug-05-2025_13-11-40] - epoch:17, time: 18.368942(s), valid (NDCG@10: 0.1590, HR@10: 0.2890), test: SKIPPED, all_time: 329.757918(s)
[Aug-05-2025_13-11-58] - epoch:18, time: 18.320376(s), valid (NDCG@10: 0.1640, HR@10: 0.2963), test: SKIPPED, all_time: 348.078294(s)
[Aug-05-2025_13-12-17] - epoch:19, time: 18.883574(s), valid (NDCG@10: 0.1658, HR@10: 0.3092), test: SKIPPED, all_time: 366.961867(s)
[Aug-05-2025_13-12-35] - epoch:20, time: 18.304774(s), valid (NDCG@10: 0.1682, HR@10: 0.3119), test: SKIPPED, all_time: 385.266641(s)
[Aug-05-2025_13-12-54] - epoch:21, time: 18.351779(s), valid (NDCG@10: 0.1760, HR@10: 0.3083), test: SKIPPED, all_time: 403.618420(s)
[Aug-05-2025_13-13-12] - epoch:22, time: 18.544843(s), valid (NDCG@10: 0.1793, HR@10: 0.3193), test: SKIPPED, all_time: 422.163263(s)
[Aug-05-2025_13-13-12] - 新的最佳性能: valid NDCG@10=0.1793, valid HR@10=0.3193
[Aug-05-2025_13-13-31] - epoch:23, time: 18.839779(s), valid (NDCG@10: 0.1862, HR@10: 0.3193), test: SKIPPED, all_time: 441.003042(s)
[Aug-05-2025_13-13-50] - epoch:24, time: 18.744601(s), valid (NDCG@10: 0.1855, HR@10: 0.3202), test: SKIPPED, all_time: 459.747643(s)
[Aug-05-2025_13-13-50] - 新的最佳性能: valid NDCG@10=0.1862, valid HR@10=0.3202
[Aug-05-2025_13-14-08] - epoch:25, time: 18.654646(s), valid (NDCG@10: 0.1845, HR@10: 0.3239), test: SKIPPED, all_time: 478.402289(s)
[Aug-05-2025_13-14-08] - 新的最佳性能: valid NDCG@10=0.1862, valid HR@10=0.3239
[Aug-05-2025_13-14-27] - epoch:26, time: 18.247889(s), valid (NDCG@10: 0.1874, HR@10: 0.3248), test: SKIPPED, all_time: 496.650178(s)
[Aug-05-2025_13-14-27] - 新的最佳性能: valid NDCG@10=0.1874, valid HR@10=0.3248
[Aug-05-2025_13-14-45] - epoch:27, time: 18.160035(s), valid (NDCG@10: 0.1889, HR@10: 0.3266), test: SKIPPED, all_time: 514.810213(s)
[Aug-05-2025_13-14-45] - 新的最佳性能: valid NDCG@10=0.1889, valid HR@10=0.3266
[Aug-05-2025_13-15-03] - epoch:28, time: 18.446432(s), valid (NDCG@10: 0.1925, HR@10: 0.3339), test: SKIPPED, all_time: 533.256645(s)
[Aug-05-2025_13-15-03] - 新的最佳性能: valid NDCG@10=0.1925, valid HR@10=0.3339
[Aug-05-2025_13-15-22] - epoch:29, time: 18.596954(s), valid (NDCG@10: 0.1887, HR@10: 0.3303), test: SKIPPED, all_time: 551.853599(s)
[Aug-05-2025_13-15-40] - epoch:30, time: 18.300869(s), valid (NDCG@10: 0.1907, HR@10: 0.3266), test: SKIPPED, all_time: 570.154468(s)
[Aug-05-2025_13-15-58] - epoch:31, time: 18.181701(s), valid (NDCG@10: 0.1893, HR@10: 0.3229), test: SKIPPED, all_time: 588.336169(s)
[Aug-05-2025_13-16-17] - epoch:32, time: 18.628504(s), valid (NDCG@10: 0.1974, HR@10: 0.3394), test: SKIPPED, all_time: 606.964674(s)
[Aug-05-2025_13-16-17] - 新的最佳性能: valid NDCG@10=0.1974, valid HR@10=0.3394
[Aug-05-2025_13-16-35] - epoch:33, time: 18.414109(s), valid (NDCG@10: 0.1926, HR@10: 0.3248), test: SKIPPED, all_time: 625.378782(s)
[Aug-05-2025_13-16-54] - epoch:34, time: 18.740021(s), valid (NDCG@10: 0.1917, HR@10: 0.3275), test: SKIPPED, all_time: 644.118803(s)
[Aug-05-2025_13-17-13] - epoch:35, time: 18.851185(s), valid (NDCG@10: 0.1959, HR@10: 0.3330), test: SKIPPED, all_time: 662.969988(s)
[Aug-05-2025_13-17-31] - epoch:36, time: 18.319729(s), valid (NDCG@10: 0.1918, HR@10: 0.3303), test: SKIPPED, all_time: 681.289716(s)
[Aug-05-2025_13-17-50] - epoch:37, time: 18.424386(s), valid (NDCG@10: 0.1849, HR@10: 0.3229), test: SKIPPED, all_time: 699.714103(s)
[Aug-05-2025_13-18-08] - epoch:38, time: 18.407737(s), valid (NDCG@10: 0.1911, HR@10: 0.3321), test: SKIPPED, all_time: 718.121840(s)
[Aug-05-2025_13-18-27] - epoch:39, time: 18.664515(s), valid (NDCG@10: 0.1881, HR@10: 0.3202), test: SKIPPED, all_time: 736.786354(s)
[Aug-05-2025_13-18-45] - epoch:40, time: 18.313356(s), valid (NDCG@10: 0.1931, HR@10: 0.3193), test: SKIPPED, all_time: 755.099710(s)
[Aug-05-2025_13-19-04] - epoch:41, time: 18.394269(s), valid (NDCG@10: 0.1866, HR@10: 0.3165), test: SKIPPED, all_time: 773.493980(s)
[Aug-05-2025_13-19-22] - epoch:42, time: 18.672675(s), valid (NDCG@10: 0.1923, HR@10: 0.3229), test: SKIPPED, all_time: 792.166655(s)
[Aug-05-2025_13-19-40] - epoch:43, time: 18.187690(s), valid (NDCG@10: 0.1909, HR@10: 0.3275), test: SKIPPED, all_time: 810.354344(s)
[Aug-05-2025_13-19-58] - epoch:44, time: 17.973980(s), valid (NDCG@10: 0.1948, HR@10: 0.3257), test: SKIPPED, all_time: 828.328324(s)
[Aug-05-2025_13-20-17] - epoch:45, time: 18.295423(s), valid (NDCG@10: 0.1898, HR@10: 0.3211), test: SKIPPED, all_time: 846.623748(s)
[Aug-05-2025_13-20-35] - epoch:46, time: 18.102944(s), valid (NDCG@10: 0.1925, HR@10: 0.3266), test: SKIPPED, all_time: 864.726691(s)
[Aug-05-2025_13-20-53] - epoch:47, time: 18.241534(s), valid (NDCG@10: 0.1864, HR@10: 0.3257), test: SKIPPED, all_time: 882.968225(s)
[Aug-05-2025_13-21-11] - epoch:48, time: 18.222716(s), valid (NDCG@10: 0.1896, HR@10: 0.3284), test: SKIPPED, all_time: 901.190941(s)
[Aug-05-2025_13-21-30] - epoch:49, time: 18.305047(s), valid (NDCG@10: 0.1853, HR@10: 0.3220), test: SKIPPED, all_time: 919.495988(s)
[Aug-05-2025_13-21-48] - epoch:50, time: 18.128205(s), valid (NDCG@10: 0.1859, HR@10: 0.3193), test: SKIPPED, all_time: 937.624193(s)
[Aug-05-2025_13-22-06] - epoch:51, time: 18.490938(s), valid (NDCG@10: 0.1866, HR@10: 0.3284), test: SKIPPED, all_time: 956.115131(s)
[Aug-05-2025_13-22-25] - epoch:52, time: 18.424663(s), valid (NDCG@10: 0.1815, HR@10: 0.3147), test: SKIPPED, all_time: 974.539794(s)
[Aug-05-2025_13-22-43] - epoch:53, time: 18.320179(s), valid (NDCG@10: 0.1836, HR@10: 0.3202), test: SKIPPED, all_time: 992.859973(s)
[Aug-05-2025_13-23-03] - epoch:54, time: 19.868430(s), valid (NDCG@10: 0.1818, HR@10: 0.3202), test: SKIPPED, all_time: 1012.728403(s)
[Aug-05-2025_13-23-21] - epoch:55, time: 18.486369(s), valid (NDCG@10: 0.1811, HR@10: 0.3183), test: SKIPPED, all_time: 1031.214772(s)
[Aug-05-2025_13-23-41] - epoch:56, time: 19.576628(s), valid (NDCG@10: 0.1840, HR@10: 0.3275), test: SKIPPED, all_time: 1050.791400(s)
[Aug-05-2025_13-24-00] - epoch:57, time: 18.869426(s), valid (NDCG@10: 0.1810, HR@10: 0.3202), test: SKIPPED, all_time: 1069.660826(s)
[Aug-05-2025_13-24-18] - epoch:58, time: 18.311273(s), valid (NDCG@10: 0.1795, HR@10: 0.3156), test: SKIPPED, all_time: 1087.972098(s)
[Aug-05-2025_13-24-36] - epoch:59, time: 18.108993(s), valid (NDCG@10: 0.1790, HR@10: 0.3156), test: SKIPPED, all_time: 1106.081091(s)
[Aug-05-2025_13-24-54] - epoch:60, time: 18.126644(s), valid (NDCG@10: 0.1786, HR@10: 0.3202), test: SKIPPED, all_time: 1124.207736(s)
[Aug-05-2025_13-25-12] - epoch:61, time: 18.076593(s), valid (NDCG@10: 0.1737, HR@10: 0.3147), test: SKIPPED, all_time: 1142.284329(s)
[Aug-05-2025_13-25-30] - epoch:62, time: 17.828940(s), valid (NDCG@10: 0.1727, HR@10: 0.3083), test: SKIPPED, all_time: 1160.113269(s)
[Aug-05-2025_13-25-48] - epoch:63, time: 17.678632(s), valid (NDCG@10: 0.1717, HR@10: 0.3073), test: SKIPPED, all_time: 1177.791901(s)
[Aug-05-2025_13-26-06] - epoch:64, time: 18.019219(s), valid (NDCG@10: 0.1758, HR@10: 0.3037), test: SKIPPED, all_time: 1195.811119(s)
[Aug-05-2025_13-26-24] - epoch:65, time: 17.891663(s), valid (NDCG@10: 0.1717, HR@10: 0.3110), test: SKIPPED, all_time: 1213.702782(s)
[Aug-05-2025_13-26-41] - epoch:66, time: 17.536598(s), valid (NDCG@10: 0.1716, HR@10: 0.3064), test: SKIPPED, all_time: 1231.239380(s)
[Aug-05-2025_13-26-59] - epoch:67, time: 17.797656(s), valid (NDCG@10: 0.1658, HR@10: 0.3101), test: SKIPPED, all_time: 1249.037036(s)
[Aug-05-2025_13-27-18] - epoch:68, time: 18.549050(s), valid (NDCG@10: 0.1694, HR@10: 0.3028), test: SKIPPED, all_time: 1267.586086(s)
[Aug-05-2025_13-27-35] - epoch:69, time: 17.665490(s), valid (NDCG@10: 0.1612, HR@10: 0.2982), test: SKIPPED, all_time: 1285.251576(s)
[Aug-05-2025_13-27-53] - epoch:70, time: 17.590471(s), valid (NDCG@10: 0.1632, HR@10: 0.2899), test: SKIPPED, all_time: 1302.842047(s)
[Aug-05-2025_13-28-11] - epoch:71, time: 18.397452(s), valid (NDCG@10: 0.1633, HR@10: 0.2908), test: SKIPPED, all_time: 1321.239499(s)
[Aug-05-2025_13-28-29] - epoch:72, time: 17.628538(s), valid (NDCG@10: 0.1615, HR@10: 0.2917), test: SKIPPED, all_time: 1338.868037(s)
[Aug-05-2025_13-28-48] - epoch:73, time: 19.262063(s), valid (NDCG@10: 0.1587, HR@10: 0.2917), test: SKIPPED, all_time: 1358.130100(s)
[Aug-05-2025_13-29-06] - epoch:74, time: 18.213636(s), valid (NDCG@10: 0.1595, HR@10: 0.2917), test: SKIPPED, all_time: 1376.343736(s)
[Aug-05-2025_13-29-24] - epoch:75, time: 17.881142(s), valid (NDCG@10: 0.1586, HR@10: 0.2872), test: SKIPPED, all_time: 1394.224878(s)
[Aug-05-2025_13-29-42] - epoch:76, time: 17.678757(s), valid (NDCG@10: 0.1599, HR@10: 0.2862), test: SKIPPED, all_time: 1411.903635(s)
[Aug-05-2025_13-30-00] - epoch:77, time: 18.105207(s), valid (NDCG@10: 0.1554, HR@10: 0.2862), test: SKIPPED, all_time: 1430.008842(s)
[Aug-05-2025_13-30-18] - epoch:78, time: 18.003268(s), valid (NDCG@10: 0.1472, HR@10: 0.2743), test: SKIPPED, all_time: 1448.012110(s)
[Aug-05-2025_13-30-36] - epoch:79, time: 17.879648(s), valid (NDCG@10: 0.1513, HR@10: 0.2743), test: SKIPPED, all_time: 1465.891758(s)
[Aug-05-2025_13-30-54] - epoch:80, time: 17.505682(s), valid (NDCG@10: 0.1487, HR@10: 0.2798), test: SKIPPED, all_time: 1483.397439(s)
[Aug-05-2025_13-31-12] - epoch:81, time: 18.002150(s), valid (NDCG@10: 0.1483, HR@10: 0.2716), test: SKIPPED, all_time: 1501.399589(s)
[Aug-05-2025_13-31-29] - epoch:82, time: 17.782275(s), valid (NDCG@10: 0.1457, HR@10: 0.2725), test: SKIPPED, all_time: 1519.181865(s)
[Aug-05-2025_13-31-47] - epoch:83, time: 17.675342(s), valid (NDCG@10: 0.1464, HR@10: 0.2798), test: SKIPPED, all_time: 1536.857207(s)
[Aug-05-2025_13-32-05] - epoch:84, time: 17.857745(s), valid (NDCG@10: 0.1463, HR@10: 0.2716), test: SKIPPED, all_time: 1554.714952(s)
[Aug-05-2025_13-32-23] - epoch:85, time: 17.941760(s), valid (NDCG@10: 0.1472, HR@10: 0.2780), test: SKIPPED, all_time: 1572.656712(s)
[Aug-05-2025_13-32-41] - epoch:86, time: 17.806282(s), valid (NDCG@10: 0.1405, HR@10: 0.2615), test: SKIPPED, all_time: 1590.462993(s)
[Aug-05-2025_13-32-58] - epoch:87, time: 17.839527(s), valid (NDCG@10: 0.1397, HR@10: 0.2706), test: SKIPPED, all_time: 1608.302520(s)
[Aug-05-2025_13-33-16] - epoch:88, time: 17.815517(s), valid (NDCG@10: 0.1434, HR@10: 0.2725), test: SKIPPED, all_time: 1626.118037(s)
[Aug-05-2025_13-33-34] - epoch:89, time: 18.191042(s), valid (NDCG@10: 0.1391, HR@10: 0.2569), test: SKIPPED, all_time: 1644.309079(s)
[Aug-05-2025_13-33-52] - epoch:90, time: 17.794044(s), valid (NDCG@10: 0.1365, HR@10: 0.2651), test: SKIPPED, all_time: 1662.103123(s)
[Aug-05-2025_13-34-11] - epoch:91, time: 18.284381(s), valid (NDCG@10: 0.1371, HR@10: 0.2670), test: SKIPPED, all_time: 1680.387504(s)
[Aug-05-2025_13-34-28] - epoch:92, time: 17.457925(s), valid (NDCG@10: 0.1337, HR@10: 0.2587), test: SKIPPED, all_time: 1697.845429(s)
[Aug-05-2025_13-34-46] - epoch:93, time: 18.130379(s), valid (NDCG@10: 0.1390, HR@10: 0.2651), test: SKIPPED, all_time: 1715.975808(s)
[Aug-05-2025_13-35-04] - epoch:94, time: 17.974195(s), valid (NDCG@10: 0.1388, HR@10: 0.2661), test: SKIPPED, all_time: 1733.950004(s)
[Aug-05-2025_13-35-22] - epoch:95, time: 18.072689(s), valid (NDCG@10: 0.1387, HR@10: 0.2670), test: SKIPPED, all_time: 1752.022693(s)
[Aug-05-2025_13-35-40] - epoch:96, time: 17.588583(s), valid (NDCG@10: 0.1316, HR@10: 0.2523), test: SKIPPED, all_time: 1769.611275(s)
[Aug-05-2025_13-35-58] - epoch:97, time: 17.827017(s), valid (NDCG@10: 0.1305, HR@10: 0.2486), test: SKIPPED, all_time: 1787.438293(s)
[Aug-05-2025_13-36-16] - epoch:98, time: 18.222949(s), valid (NDCG@10: 0.1336, HR@10: 0.2541), test: SKIPPED, all_time: 1805.661241(s)
[Aug-05-2025_13-36-34] - epoch:99, time: 17.758823(s), valid (NDCG@10: 0.1246, HR@10: 0.2495), test: SKIPPED, all_time: 1823.420064(s)
[Aug-05-2025_13-36-51] - epoch:100, time: 17.659003(s), valid (NDCG@10: 0.1300, HR@10: 0.2523), test: SKIPPED, all_time: 1841.079067(s)
[Aug-05-2025_13-37-09] - epoch:101, time: 18.038380(s), valid (NDCG@10: 0.1263, HR@10: 0.2376), test: SKIPPED, all_time: 1859.117447(s)
[Aug-05-2025_13-37-27] - epoch:102, time: 17.721151(s), valid (NDCG@10: 0.1286, HR@10: 0.2486), test: SKIPPED, all_time: 1876.838598(s)
[Aug-05-2025_13-37-45] - epoch:103, time: 17.708271(s), valid (NDCG@10: 0.1284, HR@10: 0.2495), test: SKIPPED, all_time: 1894.546869(s)
[Aug-05-2025_13-38-02] - epoch:104, time: 17.743454(s), valid (NDCG@10: 0.1236, HR@10: 0.2431), test: SKIPPED, all_time: 1912.290323(s)
[Aug-05-2025_13-38-20] - epoch:105, time: 18.033085(s), valid (NDCG@10: 0.1239, HR@10: 0.2422), test: SKIPPED, all_time: 1930.323408(s)
[Aug-05-2025_13-38-40] - epoch:106, time: 19.965171(s), valid (NDCG@10: 0.1266, HR@10: 0.2394), test: SKIPPED, all_time: 1950.288580(s)
[Aug-05-2025_13-39-02] - epoch:107, time: 21.429665(s), valid (NDCG@10: 0.1245, HR@10: 0.2404), test: SKIPPED, all_time: 1971.718245(s)
[Aug-05-2025_13-39-20] - epoch:108, time: 18.159836(s), valid (NDCG@10: 0.1263, HR@10: 0.2459), test: SKIPPED, all_time: 1989.878081(s)
[Aug-05-2025_13-39-42] - epoch:109, time: 21.689917(s), valid (NDCG@10: 0.1228, HR@10: 0.2349), test: SKIPPED, all_time: 2011.567998(s)
[Aug-05-2025_13-40-02] - epoch:110, time: 20.652406(s), valid (NDCG@10: 0.1242, HR@10: 0.2376), test: SKIPPED, all_time: 2032.220405(s)
[Aug-05-2025_13-40-20] - epoch:111, time: 18.016634(s), valid (NDCG@10: 0.1215, HR@10: 0.2321), test: SKIPPED, all_time: 2050.237039(s)
[Aug-05-2025_13-40-38] - epoch:112, time: 17.878047(s), valid (NDCG@10: 0.1238, HR@10: 0.2376), test: SKIPPED, all_time: 2068.115085(s)
[Aug-05-2025_13-40-59] - epoch:113, time: 20.362861(s), valid (NDCG@10: 0.1195, HR@10: 0.2358), test: SKIPPED, all_time: 2088.477947(s)
[Aug-05-2025_13-41-18] - epoch:114, time: 19.635588(s), valid (NDCG@10: 0.1192, HR@10: 0.2275), test: SKIPPED, all_time: 2108.113535(s)
[Aug-05-2025_13-41-36] - epoch:115, time: 18.144137(s), valid (NDCG@10: 0.1156, HR@10: 0.2294), test: SKIPPED, all_time: 2126.257672(s)
[Aug-05-2025_13-41-54] - epoch:116, time: 18.010478(s), valid (NDCG@10: 0.1184, HR@10: 0.2394), test: SKIPPED, all_time: 2144.268149(s)
[Aug-05-2025_13-42-13] - epoch:117, time: 18.358762(s), valid (NDCG@10: 0.1145, HR@10: 0.2284), test: SKIPPED, all_time: 2162.626911(s)
[Aug-05-2025_13-42-31] - epoch:118, time: 18.187345(s), valid (NDCG@10: 0.1147, HR@10: 0.2312), test: SKIPPED, all_time: 2180.814256(s)
[Aug-05-2025_13-42-49] - epoch:119, time: 18.280957(s), valid (NDCG@10: 0.1150, HR@10: 0.2275), test: SKIPPED, all_time: 2199.095213(s)
[Aug-05-2025_13-43-07] - epoch:120, time: 17.988358(s), valid (NDCG@10: 0.1140, HR@10: 0.2294), test: SKIPPED, all_time: 2217.083571(s)
[Aug-05-2025_13-43-26] - epoch:121, time: 18.380017(s), valid (NDCG@10: 0.1105, HR@10: 0.2275), test: SKIPPED, all_time: 2235.463588(s)
[Aug-05-2025_13-43-44] - epoch:122, time: 18.217128(s), valid (NDCG@10: 0.1105, HR@10: 0.2239), test: SKIPPED, all_time: 2253.680716(s)
[Aug-05-2025_13-44-02] - epoch:123, time: 18.321816(s), valid (NDCG@10: 0.1191, HR@10: 0.2303), test: SKIPPED, all_time: 2272.002532(s)
[Aug-05-2025_13-44-20] - epoch:124, time: 18.246952(s), valid (NDCG@10: 0.1116, HR@10: 0.2275), test: SKIPPED, all_time: 2290.249484(s)
[Aug-05-2025_13-44-40] - epoch:125, time: 20.045090(s), valid (NDCG@10: 0.1135, HR@10: 0.2257), test: SKIPPED, all_time: 2310.294574(s)
[Aug-05-2025_13-45-02] - epoch:126, time: 21.151195(s), valid (NDCG@10: 0.1125, HR@10: 0.2248), test: SKIPPED, all_time: 2331.445770(s)
[Aug-05-2025_13-45-20] - epoch:127, time: 18.867237(s), valid (NDCG@10: 0.1146, HR@10: 0.2275), test: SKIPPED, all_time: 2350.313007(s)
[Aug-05-2025_13-45-38] - epoch:128, time: 17.788810(s), valid (NDCG@10: 0.1116, HR@10: 0.2119), test: SKIPPED, all_time: 2368.101816(s)
[Aug-05-2025_13-45-56] - epoch:129, time: 17.846217(s), valid (NDCG@10: 0.1102, HR@10: 0.2211), test: SKIPPED, all_time: 2385.948033(s)
[Aug-05-2025_13-46-15] - epoch:130, time: 18.619321(s), valid (NDCG@10: 0.1113, HR@10: 0.2239), test: SKIPPED, all_time: 2404.567354(s)
[Aug-05-2025_13-46-33] - epoch:131, time: 18.369318(s), valid (NDCG@10: 0.1125, HR@10: 0.2165), test: SKIPPED, all_time: 2422.936673(s)
[Aug-05-2025_13-46-51] - 早停触发！NDCG在100轮内没有改善。
[Aug-05-2025_13-46-51] - epoch:132, time: 18.230354(s), valid (NDCG@10: 0.1063, HR@10: 0.2119), test: SKIPPED, all_time: 2441.167027(s)
[Aug-05-2025_13-46-51] - [联邦训练] 最佳结果: valid NDCG@10=0.1974, HR@10=0.3394 (测试集评估已跳过)
