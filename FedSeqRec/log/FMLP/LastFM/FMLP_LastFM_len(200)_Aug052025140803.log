{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 100, 'datapath': '../../data/', 'dataset': 'LastFM', 'train_data': 'LastFM.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'norm_first': True, 'c': 9, 'alpha': 0.3}
[Aug-05-2025_14-08-03] - 开始训练，配置参数如下：
[Aug-05-2025_14-08-03] - early_stop_enabled: True
[Aug-05-2025_14-08-03] - model: FMLP
[Aug-05-2025_14-08-03] - lr: 0.001
[Aug-05-2025_14-08-03] - batch_size: 128
[Aug-05-2025_14-08-03] - neg_num: 99
[Aug-05-2025_14-08-03] - l2_reg: 0
[Aug-05-2025_14-08-03] - l2_emb: 0.0
[Aug-05-2025_14-08-03] - hidden_size: 50
[Aug-05-2025_14-08-03] - dropout: 0.2
[Aug-05-2025_14-08-03] - epochs: 1000000
[Aug-05-2025_14-08-03] - early_stop: 100
[Aug-05-2025_14-08-03] - datapath: ../../data/
[Aug-05-2025_14-08-03] - dataset: LastFM
[Aug-05-2025_14-08-03] - train_data: LastFM.txt
[Aug-05-2025_14-08-03] - log_path: ../log
[Aug-05-2025_14-08-03] - num_layers: 2
[Aug-05-2025_14-08-03] - num_heads: 1
[Aug-05-2025_14-08-03] - inner_size: 256
[Aug-05-2025_14-08-03] - max_seq_len: 200
[Aug-05-2025_14-08-03] - upload_mode: full
[Aug-05-2025_14-08-03] - skip_test_eval: True
[Aug-05-2025_14-08-03] - eval_freq: 1
[Aug-05-2025_14-08-03] - norm_first: True
[Aug-05-2025_14-08-03] - c: 9
[Aug-05-2025_14-08-03] - alpha: 0.3
[Aug-05-2025_14-08-03] - 训练数据: ../../data/LastFM/LastFM.txt
[Aug-05-2025_14-08-03] - 最大序列长度: 200
[Aug-05-2025_14-08-03] - 批次大小: 128
[Aug-05-2025_14-08-06] - 参数上传模式: full
[Aug-05-2025_14-08-06] - 隐私参数（本地更新）: []
[Aug-05-2025_14-08-06] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'trm_encoder.layer.0.filter_layer.complex_weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.bias', 'trm_encoder.layer.0.feed_forward.dense_1.weight', 'trm_encoder.layer.0.feed_forward.dense_1.bias', 'trm_encoder.layer.0.feed_forward.dense_2.weight', 'trm_encoder.layer.0.feed_forward.dense_2.bias', 'trm_encoder.layer.0.feed_forward.LayerNorm.weight', 'trm_encoder.layer.0.feed_forward.LayerNorm.bias', 'trm_encoder.layer.1.filter_layer.complex_weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.bias', 'trm_encoder.layer.1.feed_forward.dense_1.weight', 'trm_encoder.layer.1.feed_forward.dense_1.bias', 'trm_encoder.layer.1.feed_forward.dense_2.weight', 'trm_encoder.layer.1.feed_forward.dense_2.bias', 'trm_encoder.layer.1.feed_forward.LayerNorm.weight', 'trm_encoder.layer.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-05-2025_14-08-06] - 用户数量: 1090
[Aug-05-2025_14-08-06] - 物品数量: 3646
[Aug-05-2025_14-08-43] - epoch:1, time: 36.203943(s), valid (NDCG@10: 0.0737, HR@10: 0.1532), test: SKIPPED, all_time: 36.203943(s)
[Aug-05-2025_14-08-43] - 新的最佳性能: valid NDCG@10=0.0737, valid HR@10=0.1532
[Aug-05-2025_14-09-17] - epoch:2, time: 34.638233(s), valid (NDCG@10: 0.1411, HR@10: 0.2716), test: SKIPPED, all_time: 70.842176(s)
[Aug-05-2025_14-09-17] - 新的最佳性能: valid NDCG@10=0.1411, valid HR@10=0.2716
[Aug-05-2025_14-09-49] - epoch:3, time: 32.026604(s), valid (NDCG@10: 0.1700, HR@10: 0.3055), test: SKIPPED, all_time: 102.868780(s)
[Aug-05-2025_14-09-49] - 新的最佳性能: valid NDCG@10=0.1700, valid HR@10=0.3055
[Aug-05-2025_14-10-18] - epoch:4, time: 28.445960(s), valid (NDCG@10: 0.1767, HR@10: 0.3119), test: SKIPPED, all_time: 131.314740(s)
[Aug-05-2025_14-10-18] - 新的最佳性能: valid NDCG@10=0.1767, valid HR@10=0.3119
[Aug-05-2025_14-10-45] - epoch:5, time: 27.302094(s), valid (NDCG@10: 0.1718, HR@10: 0.3028), test: SKIPPED, all_time: 158.616834(s)
[Aug-05-2025_14-11-12] - epoch:6, time: 27.149789(s), valid (NDCG@10: 0.1613, HR@10: 0.2945), test: SKIPPED, all_time: 185.766623(s)
[Aug-05-2025_14-11-38] - epoch:7, time: 26.229257(s), valid (NDCG@10: 0.1614, HR@10: 0.2890), test: SKIPPED, all_time: 211.995880(s)
[Aug-05-2025_14-11-59] - epoch:8, time: 21.020824(s), valid (NDCG@10: 0.1608, HR@10: 0.2798), test: SKIPPED, all_time: 233.016704(s)
[Aug-05-2025_14-12-26] - epoch:9, time: 26.334125(s), valid (NDCG@10: 0.1612, HR@10: 0.2881), test: SKIPPED, all_time: 259.350829(s)
[Aug-05-2025_14-12-49] - epoch:10, time: 22.806351(s), valid (NDCG@10: 0.1583, HR@10: 0.2835), test: SKIPPED, all_time: 282.157180(s)
[Aug-05-2025_14-13-13] - epoch:11, time: 24.554437(s), valid (NDCG@10: 0.1620, HR@10: 0.2853), test: SKIPPED, all_time: 306.711617(s)
[Aug-05-2025_14-13-39] - epoch:12, time: 25.821084(s), valid (NDCG@10: 0.1632, HR@10: 0.2881), test: SKIPPED, all_time: 332.532701(s)
[Aug-05-2025_14-14-06] - epoch:13, time: 27.564802(s), valid (NDCG@10: 0.1579, HR@10: 0.2771), test: SKIPPED, all_time: 360.097502(s)
[Aug-05-2025_14-14-30] - epoch:14, time: 23.581649(s), valid (NDCG@10: 0.1618, HR@10: 0.2844), test: SKIPPED, all_time: 383.679152(s)
[Aug-05-2025_14-14-56] - epoch:15, time: 25.597934(s), valid (NDCG@10: 0.1615, HR@10: 0.2817), test: SKIPPED, all_time: 409.277086(s)
[Aug-05-2025_14-15-20] - epoch:16, time: 24.558038(s), valid (NDCG@10: 0.1605, HR@10: 0.2936), test: SKIPPED, all_time: 433.835124(s)
[Aug-05-2025_14-15-47] - epoch:17, time: 26.707814(s), valid (NDCG@10: 0.1635, HR@10: 0.2927), test: SKIPPED, all_time: 460.542937(s)
[Aug-05-2025_14-16-16] - epoch:18, time: 29.113750(s), valid (NDCG@10: 0.1575, HR@10: 0.2771), test: SKIPPED, all_time: 489.656687(s)
[Aug-05-2025_14-16-56] - epoch:19, time: 40.224496(s), valid (NDCG@10: 0.1589, HR@10: 0.2853), test: SKIPPED, all_time: 529.881183(s)
[Aug-05-2025_14-17-45] - epoch:20, time: 48.799405(s), valid (NDCG@10: 0.1555, HR@10: 0.2807), test: SKIPPED, all_time: 578.680588(s)
[Aug-05-2025_14-18-16] - epoch:21, time: 30.894384(s), valid (NDCG@10: 0.1590, HR@10: 0.2881), test: SKIPPED, all_time: 609.574972(s)
[Aug-05-2025_14-18-41] - epoch:22, time: 25.058368(s), valid (NDCG@10: 0.1562, HR@10: 0.2725), test: SKIPPED, all_time: 634.633339(s)
[Aug-05-2025_14-19-07] - epoch:23, time: 26.170855(s), valid (NDCG@10: 0.1602, HR@10: 0.2798), test: SKIPPED, all_time: 660.804194(s)
[Aug-05-2025_14-19-32] - epoch:24, time: 24.842658(s), valid (NDCG@10: 0.1616, HR@10: 0.2872), test: SKIPPED, all_time: 685.646852(s)
[Aug-05-2025_14-19-57] - epoch:25, time: 24.786559(s), valid (NDCG@10: 0.1569, HR@10: 0.2725), test: SKIPPED, all_time: 710.433410(s)
[Aug-05-2025_14-20-24] - epoch:26, time: 26.771602(s), valid (NDCG@10: 0.1591, HR@10: 0.2761), test: SKIPPED, all_time: 737.205013(s)
[Aug-05-2025_14-20-57] - epoch:27, time: 33.081153(s), valid (NDCG@10: 0.1544, HR@10: 0.2771), test: SKIPPED, all_time: 770.286165(s)
[Aug-05-2025_14-21-39] - epoch:28, time: 41.995476(s), valid (NDCG@10: 0.1651, HR@10: 0.2826), test: SKIPPED, all_time: 812.281641(s)
