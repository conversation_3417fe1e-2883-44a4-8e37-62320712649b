{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 100, 'datapath': '../../data/', 'dataset': 'LastFM', 'train_data': 'LastFM.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': True, 'norm_first': False, 'c': 9, 'alpha': 0.3}
[Aug-05-2025_13-47-28] - 开始训练，配置参数如下：
[Aug-05-2025_13-47-28] - early_stop_enabled: True
[Aug-05-2025_13-47-28] - model: FMLP
[Aug-05-2025_13-47-28] - lr: 0.001
[Aug-05-2025_13-47-28] - batch_size: 128
[Aug-05-2025_13-47-28] - neg_num: 99
[Aug-05-2025_13-47-28] - l2_reg: 0
[Aug-05-2025_13-47-28] - l2_emb: 0.0
[Aug-05-2025_13-47-28] - hidden_size: 50
[Aug-05-2025_13-47-28] - dropout: 0.2
[Aug-05-2025_13-47-28] - epochs: 1000000
[Aug-05-2025_13-47-28] - early_stop: 100
[Aug-05-2025_13-47-28] - datapath: ../../data/
[Aug-05-2025_13-47-28] - dataset: LastFM
[Aug-05-2025_13-47-28] - train_data: LastFM.txt
[Aug-05-2025_13-47-28] - log_path: ../log
[Aug-05-2025_13-47-28] - num_layers: 2
[Aug-05-2025_13-47-28] - num_heads: 1
[Aug-05-2025_13-47-28] - inner_size: 256
[Aug-05-2025_13-47-28] - max_seq_len: 200
[Aug-05-2025_13-47-28] - upload_mode: full
[Aug-05-2025_13-47-28] - skip_test_eval: True
[Aug-05-2025_13-47-28] - eval_freq: 1
[Aug-05-2025_13-47-28] - use_dynamic_sampling: True
[Aug-05-2025_13-47-28] - norm_first: False
[Aug-05-2025_13-47-28] - c: 9
[Aug-05-2025_13-47-28] - alpha: 0.3
[Aug-05-2025_13-47-28] - 训练数据: ../../data/LastFM/LastFM.txt
[Aug-05-2025_13-47-28] - 最大序列长度: 200
[Aug-05-2025_13-47-28] - 批次大小: 128
[Aug-05-2025_13-47-29] - 参数上传模式: full
[Aug-05-2025_13-47-29] - 隐私参数（本地更新）: []
[Aug-05-2025_13-47-29] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'trm_encoder.layer.0.filter_layer.complex_weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.bias', 'trm_encoder.layer.0.feed_forward.dense_1.weight', 'trm_encoder.layer.0.feed_forward.dense_1.bias', 'trm_encoder.layer.0.feed_forward.dense_2.weight', 'trm_encoder.layer.0.feed_forward.dense_2.bias', 'trm_encoder.layer.0.feed_forward.LayerNorm.weight', 'trm_encoder.layer.0.feed_forward.LayerNorm.bias', 'trm_encoder.layer.1.filter_layer.complex_weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.bias', 'trm_encoder.layer.1.feed_forward.dense_1.weight', 'trm_encoder.layer.1.feed_forward.dense_1.bias', 'trm_encoder.layer.1.feed_forward.dense_2.weight', 'trm_encoder.layer.1.feed_forward.dense_2.bias', 'trm_encoder.layer.1.feed_forward.LayerNorm.weight', 'trm_encoder.layer.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-05-2025_13-47-29] - 动态负采样: True
[Aug-05-2025_13-47-29] - 用户数量: 1090
[Aug-05-2025_13-47-29] - 物品数量: 3646
[Aug-05-2025_13-48-09] - epoch:1, time: 39.947171(s), valid (NDCG@10: 0.0677, HR@10: 0.1385), test: SKIPPED, all_time: 39.947171(s)
[Aug-05-2025_13-48-09] - 新的最佳性能: valid NDCG@10=0.0677, valid HR@10=0.1385
[Aug-05-2025_13-48-50] - epoch:2, time: 41.124412(s), valid (NDCG@10: 0.1308, HR@10: 0.2587), test: SKIPPED, all_time: 81.071583(s)
[Aug-05-2025_13-48-50] - 新的最佳性能: valid NDCG@10=0.1308, valid HR@10=0.2587
[Aug-05-2025_13-49-33] - epoch:3, time: 43.062674(s), valid (NDCG@10: 0.1585, HR@10: 0.2945), test: SKIPPED, all_time: 124.134257(s)
[Aug-05-2025_13-49-33] - 新的最佳性能: valid NDCG@10=0.1585, valid HR@10=0.2945
[Aug-05-2025_13-50-18] - epoch:4, time: 44.493896(s), valid (NDCG@10: 0.1643, HR@10: 0.3147), test: SKIPPED, all_time: 168.628154(s)
[Aug-05-2025_13-50-18] - 新的最佳性能: valid NDCG@10=0.1643, valid HR@10=0.3147
[Aug-05-2025_13-51-24] - epoch:5, time: 66.567423(s), valid (NDCG@10: 0.1589, HR@10: 0.2991), test: SKIPPED, all_time: 235.195577(s)
[Aug-05-2025_13-52-26] - epoch:6, time: 61.923451(s), valid (NDCG@10: 0.1608, HR@10: 0.2936), test: SKIPPED, all_time: 297.119027(s)
[Aug-05-2025_13-53-21] - epoch:7, time: 54.345425(s), valid (NDCG@10: 0.1547, HR@10: 0.2826), test: SKIPPED, all_time: 351.464453(s)
[Aug-05-2025_13-54-22] - epoch:8, time: 61.225848(s), valid (NDCG@10: 0.1594, HR@10: 0.2917), test: SKIPPED, all_time: 412.690301(s)
[Aug-05-2025_13-55-19] - epoch:9, time: 56.624464(s), valid (NDCG@10: 0.1520, HR@10: 0.2789), test: SKIPPED, all_time: 469.314765(s)
[Aug-05-2025_13-56-08] - epoch:10, time: 49.393390(s), valid (NDCG@10: 0.1517, HR@10: 0.2734), test: SKIPPED, all_time: 518.708155(s)
[Aug-05-2025_13-57-00] - epoch:11, time: 51.553106(s), valid (NDCG@10: 0.1508, HR@10: 0.2679), test: SKIPPED, all_time: 570.261261(s)
[Aug-05-2025_13-57-48] - epoch:12, time: 48.886980(s), valid (NDCG@10: 0.1439, HR@10: 0.2633), test: SKIPPED, all_time: 619.148241(s)
