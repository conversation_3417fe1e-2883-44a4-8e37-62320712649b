{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'LastFM', 'train_data': 'LastFM.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False, 'c': 9, 'alpha': 0.3}
[Aug-05-2025_11-02-52] - 开始训练，配置参数如下：
[Aug-05-2025_11-02-52] - early_stop_enabled: True
[Aug-05-2025_11-02-52] - model: FMLP
[Aug-05-2025_11-02-52] - lr: 0.001
[Aug-05-2025_11-02-52] - batch_size: 128
[Aug-05-2025_11-02-52] - neg_num: 99
[Aug-05-2025_11-02-52] - l2_reg: 0
[Aug-05-2025_11-02-52] - l2_emb: 0.0
[Aug-05-2025_11-02-52] - hidden_size: 50
[Aug-05-2025_11-02-52] - dropout: 0.2
[Aug-05-2025_11-02-52] - epochs: 1000000
[Aug-05-2025_11-02-52] - early_stop: 50
[Aug-05-2025_11-02-52] - datapath: ../../data/
[Aug-05-2025_11-02-52] - dataset: LastFM
[Aug-05-2025_11-02-52] - train_data: LastFM.txt
[Aug-05-2025_11-02-52] - log_path: ../log
[Aug-05-2025_11-02-52] - num_layers: 2
[Aug-05-2025_11-02-52] - num_heads: 1
[Aug-05-2025_11-02-52] - inner_size: 256
[Aug-05-2025_11-02-52] - max_seq_len: 200
[Aug-05-2025_11-02-52] - upload_mode: full
[Aug-05-2025_11-02-52] - skip_test_eval: True
[Aug-05-2025_11-02-52] - eval_freq: 1
[Aug-05-2025_11-02-52] - use_dynamic_sampling: False
[Aug-05-2025_11-02-52] - norm_first: False
[Aug-05-2025_11-02-52] - c: 9
[Aug-05-2025_11-02-52] - alpha: 0.3
[Aug-05-2025_11-02-52] - 训练数据: ../../data/LastFM/LastFM.txt
[Aug-05-2025_11-02-52] - 最大序列长度: 200
[Aug-05-2025_11-02-52] - 批次大小: 128
[Aug-05-2025_11-02-53] - 参数上传模式: full
[Aug-05-2025_11-02-53] - 隐私参数（本地更新）: []
[Aug-05-2025_11-02-53] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'trm_encoder.layer.0.filter_layer.complex_weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.bias', 'trm_encoder.layer.0.feed_forward.dense_1.weight', 'trm_encoder.layer.0.feed_forward.dense_1.bias', 'trm_encoder.layer.0.feed_forward.dense_2.weight', 'trm_encoder.layer.0.feed_forward.dense_2.bias', 'trm_encoder.layer.0.feed_forward.LayerNorm.weight', 'trm_encoder.layer.0.feed_forward.LayerNorm.bias', 'trm_encoder.layer.1.filter_layer.complex_weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.bias', 'trm_encoder.layer.1.feed_forward.dense_1.weight', 'trm_encoder.layer.1.feed_forward.dense_1.bias', 'trm_encoder.layer.1.feed_forward.dense_2.weight', 'trm_encoder.layer.1.feed_forward.dense_2.bias', 'trm_encoder.layer.1.feed_forward.LayerNorm.weight', 'trm_encoder.layer.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-05-2025_11-02-53] - 动态负采样: False
[Aug-05-2025_11-02-53] - 用户数量: 1090
[Aug-05-2025_11-02-53] - 物品数量: 3646
[Aug-05-2025_11-03-08] - epoch:1, time: 11.421405(s), valid (NDCG@10: 0.0621, HR@10: 0.1431), test: SKIPPED, all_time: 11.421405(s)
[Aug-05-2025_11-03-08] - 新的最佳性能: valid NDCG@10=0.0621, valid HR@10=0.1431
[Aug-05-2025_11-03-22] - epoch:2, time: 10.909555(s), valid (NDCG@10: 0.1309, HR@10: 0.2477), test: SKIPPED, all_time: 22.330960(s)
[Aug-05-2025_11-03-22] - 新的最佳性能: valid NDCG@10=0.1309, valid HR@10=0.2477
[Aug-05-2025_11-03-37] - epoch:3, time: 11.132957(s), valid (NDCG@10: 0.1540, HR@10: 0.2826), test: SKIPPED, all_time: 33.463917(s)
[Aug-05-2025_11-03-37] - 新的最佳性能: valid NDCG@10=0.1540, valid HR@10=0.2826
[Aug-05-2025_11-03-53] - epoch:4, time: 11.402742(s), valid (NDCG@10: 0.1617, HR@10: 0.2982), test: SKIPPED, all_time: 44.866659(s)
[Aug-05-2025_11-03-53] - 新的最佳性能: valid NDCG@10=0.1617, valid HR@10=0.2982
[Aug-05-2025_11-04-07] - epoch:5, time: 10.992153(s), valid (NDCG@10: 0.1553, HR@10: 0.2826), test: SKIPPED, all_time: 55.858812(s)
[Aug-05-2025_11-04-26] - epoch:6, time: 14.118399(s), valid (NDCG@10: 0.1532, HR@10: 0.2780), test: SKIPPED, all_time: 69.977211(s)
[Aug-05-2025_11-04-46] - epoch:7, time: 11.399094(s), valid (NDCG@10: 0.1467, HR@10: 0.2615), test: SKIPPED, all_time: 81.376306(s)
[Aug-05-2025_11-05-06] - epoch:8, time: 16.824015(s), valid (NDCG@10: 0.1471, HR@10: 0.2725), test: SKIPPED, all_time: 98.200320(s)
[Aug-05-2025_11-05-23] - epoch:9, time: 12.198000(s), valid (NDCG@10: 0.1469, HR@10: 0.2670), test: SKIPPED, all_time: 110.398320(s)
[Aug-05-2025_11-05-46] - epoch:10, time: 19.184165(s), valid (NDCG@10: 0.1438, HR@10: 0.2688), test: SKIPPED, all_time: 129.582485(s)
[Aug-05-2025_11-06-07] - epoch:11, time: 14.995373(s), valid (NDCG@10: 0.1546, HR@10: 0.2734), test: SKIPPED, all_time: 144.577858(s)
[Aug-05-2025_11-06-21] - epoch:12, time: 11.427485(s), valid (NDCG@10: 0.1465, HR@10: 0.2615), test: SKIPPED, all_time: 156.005342(s)
[Aug-05-2025_11-06-40] - epoch:13, time: 14.510083(s), valid (NDCG@10: 0.1470, HR@10: 0.2734), test: SKIPPED, all_time: 170.515426(s)
[Aug-05-2025_11-06-54] - epoch:14, time: 11.264096(s), valid (NDCG@10: 0.1532, HR@10: 0.2642), test: SKIPPED, all_time: 181.779522(s)
[Aug-05-2025_11-07-09] - epoch:15, time: 11.129936(s), valid (NDCG@10: 0.1561, HR@10: 0.2826), test: SKIPPED, all_time: 192.909458(s)
[Aug-05-2025_11-07-24] - epoch:16, time: 11.539636(s), valid (NDCG@10: 0.1525, HR@10: 0.2798), test: SKIPPED, all_time: 204.449094(s)
[Aug-05-2025_11-07-39] - epoch:17, time: 11.164650(s), valid (NDCG@10: 0.1580, HR@10: 0.2844), test: SKIPPED, all_time: 215.613744(s)
[Aug-05-2025_11-07-55] - epoch:18, time: 11.094053(s), valid (NDCG@10: 0.1623, HR@10: 0.2862), test: SKIPPED, all_time: 226.707797(s)
[Aug-05-2025_11-08-19] - epoch:19, time: 18.394870(s), valid (NDCG@10: 0.1550, HR@10: 0.2761), test: SKIPPED, all_time: 245.102667(s)
[Aug-05-2025_11-08-35] - epoch:20, time: 11.743616(s), valid (NDCG@10: 0.1587, HR@10: 0.2835), test: SKIPPED, all_time: 256.846283(s)
[Aug-05-2025_11-08-51] - epoch:21, time: 12.195245(s), valid (NDCG@10: 0.1665, HR@10: 0.2881), test: SKIPPED, all_time: 269.041528(s)
[Aug-05-2025_11-09-08] - epoch:22, time: 11.906749(s), valid (NDCG@10: 0.1639, HR@10: 0.2844), test: SKIPPED, all_time: 280.948276(s)
[Aug-05-2025_11-09-24] - epoch:23, time: 12.867770(s), valid (NDCG@10: 0.1672, HR@10: 0.2982), test: SKIPPED, all_time: 293.816046(s)
[Aug-05-2025_11-09-41] - epoch:24, time: 12.083125(s), valid (NDCG@10: 0.1732, HR@10: 0.3064), test: SKIPPED, all_time: 305.899171(s)
[Aug-05-2025_11-09-41] - 新的最佳性能: valid NDCG@10=0.1732, valid HR@10=0.3064
[Aug-05-2025_11-09-58] - epoch:25, time: 13.015601(s), valid (NDCG@10: 0.1757, HR@10: 0.3092), test: SKIPPED, all_time: 318.914772(s)
[Aug-05-2025_11-09-58] - 新的最佳性能: valid NDCG@10=0.1757, valid HR@10=0.3092
[Aug-05-2025_11-10-14] - epoch:26, time: 12.011315(s), valid (NDCG@10: 0.1761, HR@10: 0.3064), test: SKIPPED, all_time: 330.926087(s)
[Aug-05-2025_11-10-30] - epoch:27, time: 11.478338(s), valid (NDCG@10: 0.1718, HR@10: 0.3128), test: SKIPPED, all_time: 342.404425(s)
[Aug-05-2025_11-10-30] - 新的最佳性能: valid NDCG@10=0.1761, valid HR@10=0.3128
[Aug-05-2025_11-10-46] - epoch:28, time: 12.398624(s), valid (NDCG@10: 0.1802, HR@10: 0.3119), test: SKIPPED, all_time: 354.803049(s)
[Aug-05-2025_11-11-04] - epoch:29, time: 13.673155(s), valid (NDCG@10: 0.1773, HR@10: 0.3101), test: SKIPPED, all_time: 368.476203(s)
[Aug-05-2025_11-11-19] - epoch:30, time: 11.356663(s), valid (NDCG@10: 0.1783, HR@10: 0.3128), test: SKIPPED, all_time: 379.832866(s)
[Aug-05-2025_11-11-36] - epoch:31, time: 12.098313(s), valid (NDCG@10: 0.1820, HR@10: 0.3183), test: SKIPPED, all_time: 391.931180(s)
[Aug-05-2025_11-11-36] - 新的最佳性能: valid NDCG@10=0.1820, valid HR@10=0.3183
[Aug-05-2025_11-11-51] - epoch:32, time: 11.745529(s), valid (NDCG@10: 0.1819, HR@10: 0.3193), test: SKIPPED, all_time: 403.676709(s)
[Aug-05-2025_11-11-51] - 新的最佳性能: valid NDCG@10=0.1820, valid HR@10=0.3193
[Aug-05-2025_11-12-09] - epoch:33, time: 13.142613(s), valid (NDCG@10: 0.1761, HR@10: 0.3073), test: SKIPPED, all_time: 416.819322(s)
[Aug-05-2025_11-12-26] - epoch:34, time: 13.058500(s), valid (NDCG@10: 0.1863, HR@10: 0.3248), test: SKIPPED, all_time: 429.877822(s)
[Aug-05-2025_11-12-26] - 新的最佳性能: valid NDCG@10=0.1863, valid HR@10=0.3248
[Aug-05-2025_11-12-44] - epoch:35, time: 12.751490(s), valid (NDCG@10: 0.1786, HR@10: 0.3037), test: SKIPPED, all_time: 442.629312(s)
[Aug-05-2025_11-13-00] - epoch:36, time: 11.697300(s), valid (NDCG@10: 0.1779, HR@10: 0.3147), test: SKIPPED, all_time: 454.326612(s)
[Aug-05-2025_11-13-17] - epoch:37, time: 13.010639(s), valid (NDCG@10: 0.1807, HR@10: 0.3119), test: SKIPPED, all_time: 467.337251(s)
[Aug-05-2025_11-13-39] - epoch:38, time: 16.115836(s), valid (NDCG@10: 0.1815, HR@10: 0.3183), test: SKIPPED, all_time: 483.453088(s)
[Aug-05-2025_11-13-56] - epoch:39, time: 12.873387(s), valid (NDCG@10: 0.1833, HR@10: 0.3183), test: SKIPPED, all_time: 496.326474(s)
[Aug-05-2025_11-14-13] - epoch:40, time: 12.769516(s), valid (NDCG@10: 0.1807, HR@10: 0.3083), test: SKIPPED, all_time: 509.095990(s)
[Aug-05-2025_11-14-30] - epoch:41, time: 12.871955(s), valid (NDCG@10: 0.1830, HR@10: 0.3229), test: SKIPPED, all_time: 521.967946(s)
[Aug-05-2025_11-14-48] - epoch:42, time: 13.393965(s), valid (NDCG@10: 0.1837, HR@10: 0.3110), test: SKIPPED, all_time: 535.361910(s)
[Aug-05-2025_11-15-07] - epoch:43, time: 14.680669(s), valid (NDCG@10: 0.1821, HR@10: 0.3229), test: SKIPPED, all_time: 550.042579(s)
[Aug-05-2025_11-15-26] - epoch:44, time: 13.723984(s), valid (NDCG@10: 0.1833, HR@10: 0.3239), test: SKIPPED, all_time: 563.766563(s)
[Aug-05-2025_11-15-43] - epoch:45, time: 13.847875(s), valid (NDCG@10: 0.1763, HR@10: 0.3138), test: SKIPPED, all_time: 577.614438(s)
[Aug-05-2025_11-16-02] - epoch:46, time: 14.214831(s), valid (NDCG@10: 0.1790, HR@10: 0.3110), test: SKIPPED, all_time: 591.829269(s)
[Aug-05-2025_11-16-21] - epoch:47, time: 13.592006(s), valid (NDCG@10: 0.1787, HR@10: 0.3138), test: SKIPPED, all_time: 605.421275(s)
[Aug-05-2025_11-16-40] - epoch:48, time: 14.979821(s), valid (NDCG@10: 0.1771, HR@10: 0.3147), test: SKIPPED, all_time: 620.401096(s)
[Aug-05-2025_11-16-57] - epoch:49, time: 12.729159(s), valid (NDCG@10: 0.1738, HR@10: 0.3009), test: SKIPPED, all_time: 633.130255(s)
[Aug-05-2025_11-17-15] - epoch:50, time: 14.006566(s), valid (NDCG@10: 0.1779, HR@10: 0.3064), test: SKIPPED, all_time: 647.136821(s)
[Aug-05-2025_11-17-33] - epoch:51, time: 13.331456(s), valid (NDCG@10: 0.1791, HR@10: 0.3138), test: SKIPPED, all_time: 660.468277(s)
[Aug-05-2025_11-17-51] - epoch:52, time: 13.189969(s), valid (NDCG@10: 0.1743, HR@10: 0.3092), test: SKIPPED, all_time: 673.658246(s)
[Aug-05-2025_11-18-09] - epoch:53, time: 13.236853(s), valid (NDCG@10: 0.1742, HR@10: 0.3018), test: SKIPPED, all_time: 686.895099(s)
[Aug-05-2025_11-18-26] - epoch:54, time: 12.433567(s), valid (NDCG@10: 0.1758, HR@10: 0.3055), test: SKIPPED, all_time: 699.328665(s)
[Aug-05-2025_11-18-44] - epoch:55, time: 13.684515(s), valid (NDCG@10: 0.1747, HR@10: 0.2991), test: SKIPPED, all_time: 713.013180(s)
[Aug-05-2025_11-19-01] - epoch:56, time: 12.660124(s), valid (NDCG@10: 0.1679, HR@10: 0.2917), test: SKIPPED, all_time: 725.673304(s)
[Aug-05-2025_11-19-19] - epoch:57, time: 13.905814(s), valid (NDCG@10: 0.1718, HR@10: 0.3009), test: SKIPPED, all_time: 739.579118(s)
[Aug-05-2025_11-19-38] - epoch:58, time: 13.653520(s), valid (NDCG@10: 0.1730, HR@10: 0.3000), test: SKIPPED, all_time: 753.232638(s)
[Aug-05-2025_11-20-03] - epoch:59, time: 18.169015(s), valid (NDCG@10: 0.1662, HR@10: 0.2908), test: SKIPPED, all_time: 771.401653(s)
[Aug-05-2025_11-20-32] - epoch:60, time: 21.059273(s), valid (NDCG@10: 0.1647, HR@10: 0.2862), test: SKIPPED, all_time: 792.460926(s)
[Aug-05-2025_11-21-04] - epoch:61, time: 20.581995(s), valid (NDCG@10: 0.1612, HR@10: 0.2872), test: SKIPPED, all_time: 813.042921(s)
[Aug-05-2025_11-21-56] - epoch:62, time: 40.678663(s), valid (NDCG@10: 0.1608, HR@10: 0.2853), test: SKIPPED, all_time: 853.721585(s)
[Aug-05-2025_11-22-38] - epoch:63, time: 30.417047(s), valid (NDCG@10: 0.1545, HR@10: 0.2688), test: SKIPPED, all_time: 884.138632(s)
[Aug-05-2025_11-23-16] - epoch:64, time: 28.496501(s), valid (NDCG@10: 0.1546, HR@10: 0.2761), test: SKIPPED, all_time: 912.635133(s)
[Aug-05-2025_11-23-53] - epoch:65, time: 27.708750(s), valid (NDCG@10: 0.1606, HR@10: 0.2835), test: SKIPPED, all_time: 940.343883(s)
[Aug-05-2025_11-24-29] - epoch:66, time: 26.415409(s), valid (NDCG@10: 0.1573, HR@10: 0.2835), test: SKIPPED, all_time: 966.759292(s)
[Aug-05-2025_11-25-04] - epoch:67, time: 26.733730(s), valid (NDCG@10: 0.1573, HR@10: 0.2734), test: SKIPPED, all_time: 993.493022(s)
[Aug-05-2025_11-25-45] - epoch:68, time: 26.152037(s), valid (NDCG@10: 0.1507, HR@10: 0.2743), test: SKIPPED, all_time: 1019.645059(s)
[Aug-05-2025_11-26-34] - epoch:69, time: 38.201046(s), valid (NDCG@10: 0.1543, HR@10: 0.2752), test: SKIPPED, all_time: 1057.846105(s)
[Aug-05-2025_11-27-20] - epoch:70, time: 36.047117(s), valid (NDCG@10: 0.1509, HR@10: 0.2706), test: SKIPPED, all_time: 1093.893223(s)
[Aug-05-2025_11-27-55] - epoch:71, time: 25.432419(s), valid (NDCG@10: 0.1466, HR@10: 0.2661), test: SKIPPED, all_time: 1119.325642(s)
[Aug-05-2025_11-28-31] - epoch:72, time: 27.239712(s), valid (NDCG@10: 0.1466, HR@10: 0.2688), test: SKIPPED, all_time: 1146.565355(s)
[Aug-05-2025_11-29-08] - epoch:73, time: 28.247444(s), valid (NDCG@10: 0.1503, HR@10: 0.2688), test: SKIPPED, all_time: 1174.812799(s)
[Aug-05-2025_11-29-43] - epoch:74, time: 25.995061(s), valid (NDCG@10: 0.1444, HR@10: 0.2633), test: SKIPPED, all_time: 1200.807860(s)
[Aug-05-2025_11-30-10] - epoch:75, time: 20.591724(s), valid (NDCG@10: 0.1446, HR@10: 0.2596), test: SKIPPED, all_time: 1221.399584(s)
[Aug-05-2025_11-30-35] - epoch:76, time: 19.237181(s), valid (NDCG@10: 0.1422, HR@10: 0.2569), test: SKIPPED, all_time: 1240.636765(s)
[Aug-05-2025_11-30-57] - epoch:77, time: 18.093029(s), valid (NDCG@10: 0.1387, HR@10: 0.2569), test: SKIPPED, all_time: 1258.729794(s)
[Aug-05-2025_11-31-20] - epoch:78, time: 16.586828(s), valid (NDCG@10: 0.1382, HR@10: 0.2550), test: SKIPPED, all_time: 1275.316623(s)
[Aug-05-2025_11-31-41] - epoch:79, time: 15.521591(s), valid (NDCG@10: 0.1339, HR@10: 0.2642), test: SKIPPED, all_time: 1290.838214(s)
[Aug-05-2025_11-32-02] - epoch:80, time: 14.284975(s), valid (NDCG@10: 0.1349, HR@10: 0.2523), test: SKIPPED, all_time: 1305.123189(s)
[Aug-05-2025_11-32-22] - epoch:81, time: 14.868108(s), valid (NDCG@10: 0.1293, HR@10: 0.2422), test: SKIPPED, all_time: 1319.991297(s)
[Aug-05-2025_11-32-40] - epoch:82, time: 13.592730(s), valid (NDCG@10: 0.1341, HR@10: 0.2477), test: SKIPPED, all_time: 1333.584027(s)
[Aug-05-2025_11-33-02] - epoch:83, time: 16.997587(s), valid (NDCG@10: 0.1246, HR@10: 0.2358), test: SKIPPED, all_time: 1350.581614(s)
[Aug-05-2025_11-33-20] - 早停触发！NDCG在50轮内没有改善。
[Aug-05-2025_11-33-20] - epoch:84, time: 14.138254(s), valid (NDCG@10: 0.1298, HR@10: 0.2468), test: SKIPPED, all_time: 1364.719868(s)
[Aug-05-2025_11-33-20] - [联邦训练] 最佳结果: valid NDCG@10=0.1863, HR@10=0.3248 (测试集评估已跳过)
