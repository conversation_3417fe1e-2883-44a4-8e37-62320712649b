{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000, 'early_stop': 30, 'datapath': '../../data/', 'dataset': 'Toys_and_Games', 'train_data': 'Toys_and_Games.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': False, 'eval_freq': 1, 'c': 9, 'alpha': 0.3}
[Aug-08-2025_12-41-22] - 开始训练，配置参数如下：
[Aug-08-2025_12-41-22] - early_stop_enabled: True
[Aug-08-2025_12-41-22] - model: FMLP
[Aug-08-2025_12-41-22] - lr: 0.001
[Aug-08-2025_12-41-22] - batch_size: 128
[Aug-08-2025_12-41-22] - neg_num: 99
[Aug-08-2025_12-41-22] - l2_reg: 0
[Aug-08-2025_12-41-22] - l2_emb: 0.0
[Aug-08-2025_12-41-22] - hidden_size: 50
[Aug-08-2025_12-41-22] - dropout: 0.2
[Aug-08-2025_12-41-22] - epochs: 1000
[Aug-08-2025_12-41-22] - early_stop: 30
[Aug-08-2025_12-41-22] - datapath: ../../data/
[Aug-08-2025_12-41-22] - dataset: Toys_and_Games
[Aug-08-2025_12-41-22] - train_data: Toys_and_Games.txt
[Aug-08-2025_12-41-22] - log_path: ../log
[Aug-08-2025_12-41-22] - num_layers: 2
[Aug-08-2025_12-41-22] - num_heads: 1
[Aug-08-2025_12-41-22] - inner_size: 256
[Aug-08-2025_12-41-22] - max_seq_len: 200
[Aug-08-2025_12-41-22] - upload_mode: full
[Aug-08-2025_12-41-22] - skip_test_eval: False
[Aug-08-2025_12-41-22] - eval_freq: 1
[Aug-08-2025_12-41-22] - c: 9
[Aug-08-2025_12-41-22] - alpha: 0.3
[Aug-08-2025_12-41-22] - 训练数据: ../../data/Toys_and_Games/Toys_and_Games.txt
[Aug-08-2025_12-41-22] - 最大序列长度: 200
[Aug-08-2025_12-41-22] - 批次大小: 128
[Aug-08-2025_12-41-23] - 参数上传模式: full
[Aug-08-2025_12-41-23] - 隐私参数（本地更新）: []
[Aug-08-2025_12-41-23] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'trm_encoder.layer.0.filter_layer.complex_weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.bias', 'trm_encoder.layer.0.feed_forward.dense_1.weight', 'trm_encoder.layer.0.feed_forward.dense_1.bias', 'trm_encoder.layer.0.feed_forward.dense_2.weight', 'trm_encoder.layer.0.feed_forward.dense_2.bias', 'trm_encoder.layer.0.feed_forward.LayerNorm.weight', 'trm_encoder.layer.0.feed_forward.LayerNorm.bias', 'trm_encoder.layer.1.filter_layer.complex_weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.bias', 'trm_encoder.layer.1.feed_forward.dense_1.weight', 'trm_encoder.layer.1.feed_forward.dense_1.bias', 'trm_encoder.layer.1.feed_forward.dense_2.weight', 'trm_encoder.layer.1.feed_forward.dense_2.bias', 'trm_encoder.layer.1.feed_forward.LayerNorm.weight', 'trm_encoder.layer.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-08-2025_12-41-23] - 用户数量: 19412
[Aug-08-2025_12-41-23] - 物品数量: 11924
[Aug-08-2025_12-43-52] - epoch:1, time: 149.496828(s), valid (NDCG@10: 0.1085, HR@10: 0.2162), test (NDCG@10: 0.0998, HR@10: 0.1990) all_time: 149.496828(s)
[Aug-08-2025_12-43-52] - 新的最佳性能: valid NDCG@10=0.1085, test NDCG@10=0.0998
[Aug-08-2025_12-46-14] - epoch:2, time: 141.470634(s), valid (NDCG@10: 0.1382, HR@10: 0.2727), test (NDCG@10: 0.1193, HR@10: 0.2351) all_time: 290.967461(s)
[Aug-08-2025_12-46-14] - 新的最佳性能: valid NDCG@10=0.1382, test NDCG@10=0.1193
[Aug-08-2025_12-48-24] - epoch:3, time: 130.420055(s), valid (NDCG@10: 0.1869, HR@10: 0.3433), test (NDCG@10: 0.1605, HR@10: 0.2999) all_time: 421.387517(s)
[Aug-08-2025_12-48-24] - 新的最佳性能: valid NDCG@10=0.1869, test NDCG@10=0.1605
[Aug-08-2025_12-50-32] - epoch:4, time: 128.003211(s), valid (NDCG@10: 0.2257, HR@10: 0.3944), test (NDCG@10: 0.1885, HR@10: 0.3440) all_time: 549.390728(s)
[Aug-08-2025_12-50-32] - 新的最佳性能: valid NDCG@10=0.2257, test NDCG@10=0.1885
[Aug-08-2025_12-52-54] - epoch:5, time: 141.369495(s), valid (NDCG@10: 0.2442, HR@10: 0.4225), test (NDCG@10: 0.2089, HR@10: 0.3690) all_time: 690.760224(s)
[Aug-08-2025_12-52-54] - 新的最佳性能: valid NDCG@10=0.2442, test NDCG@10=0.2089
[Aug-08-2025_12-55-10] - epoch:6, time: 136.054596(s), valid (NDCG@10: 0.2511, HR@10: 0.4304), test (NDCG@10: 0.2067, HR@10: 0.3668) all_time: 826.814820(s)
[Aug-08-2025_12-55-10] - 新的最佳性能: valid NDCG@10=0.2511, test NDCG@10=0.2089
[Aug-08-2025_12-57-31] - epoch:7, time: 141.136574(s), valid (NDCG@10: 0.2413, HR@10: 0.4087), test (NDCG@10: 0.2068, HR@10: 0.3672) all_time: 967.951394(s)
[Aug-08-2025_12-59-52] - epoch:8, time: 140.774270(s), valid (NDCG@10: 0.2411, HR@10: 0.4085), test (NDCG@10: 0.2066, HR@10: 0.3610) all_time: 1108.725664(s)
[Aug-08-2025_13-02-13] - epoch:9, time: 141.343863(s), valid (NDCG@10: 0.2253, HR@10: 0.3907), test (NDCG@10: 0.1951, HR@10: 0.3432) all_time: 1250.069526(s)
[Aug-08-2025_13-04-34] - epoch:10, time: 140.636478(s), valid (NDCG@10: 0.2178, HR@10: 0.3822), test (NDCG@10: 0.1931, HR@10: 0.3459) all_time: 1390.706005(s)
[Aug-08-2025_13-06-50] - epoch:11, time: 136.723465(s), valid (NDCG@10: 0.2100, HR@10: 0.3673), test (NDCG@10: 0.1883, HR@10: 0.3333) all_time: 1527.429470(s)
[Aug-08-2025_13-09-05] - epoch:12, time: 134.895802(s), valid (NDCG@10: 0.2076, HR@10: 0.3608), test (NDCG@10: 0.1853, HR@10: 0.3322) all_time: 1662.325273(s)
[Aug-08-2025_13-11-25] - epoch:13, time: 139.935981(s), valid (NDCG@10: 0.1981, HR@10: 0.3473), test (NDCG@10: 0.1837, HR@10: 0.3255) all_time: 1802.261254(s)
[Aug-08-2025_13-13-49] - epoch:14, time: 143.546343(s), valid (NDCG@10: 0.2001, HR@10: 0.3448), test (NDCG@10: 0.1832, HR@10: 0.3228) all_time: 1945.807597(s)
[Aug-08-2025_13-16-12] - epoch:15, time: 143.313129(s), valid (NDCG@10: 0.2004, HR@10: 0.3468), test (NDCG@10: 0.1923, HR@10: 0.3367) all_time: 2089.120726(s)
[Aug-08-2025_13-18-29] - epoch:16, time: 137.418293(s), valid (NDCG@10: 0.1999, HR@10: 0.3435), test (NDCG@10: 0.1881, HR@10: 0.3283) all_time: 2226.539018(s)
[Aug-08-2025_13-20-45] - epoch:17, time: 135.929461(s), valid (NDCG@10: 0.1953, HR@10: 0.3388), test (NDCG@10: 0.1808, HR@10: 0.3228) all_time: 2362.468479(s)
[Aug-08-2025_13-23-00] - epoch:18, time: 134.275383(s), valid (NDCG@10: 0.1993, HR@10: 0.3477), test (NDCG@10: 0.1914, HR@10: 0.3344) all_time: 2496.743862(s)
[Aug-08-2025_13-25-14] - epoch:19, time: 133.874652(s), valid (NDCG@10: 0.2002, HR@10: 0.3389), test (NDCG@10: 0.1887, HR@10: 0.3242) all_time: 2630.618514(s)
[Aug-08-2025_13-27-27] - epoch:20, time: 133.724260(s), valid (NDCG@10: 0.1957, HR@10: 0.3334), test (NDCG@10: 0.1919, HR@10: 0.3279) all_time: 2764.342774(s)
[Aug-08-2025_13-29-41] - epoch:21, time: 134.080980(s), valid (NDCG@10: 0.1892, HR@10: 0.3191), test (NDCG@10: 0.1877, HR@10: 0.3237) all_time: 2898.423754(s)
[Aug-08-2025_13-31-51] - epoch:22, time: 129.383972(s), valid (NDCG@10: 0.2006, HR@10: 0.3395), test (NDCG@10: 0.1893, HR@10: 0.3255) all_time: 3027.807726(s)
