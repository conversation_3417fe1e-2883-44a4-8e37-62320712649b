{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'embed_dim': 50, 'hidden_size': 32, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'ml-1m', 'train_data': 'ml-1m.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': False, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False}
[Aug-03-2025_16-56-30] - 开始训练，配置参数如下：
[Aug-03-2025_16-56-30] - early_stop_enabled: True
[Aug-03-2025_16-56-30] - model: FMLP
[Aug-03-2025_16-56-30] - lr: 0.001
[Aug-03-2025_16-56-30] - batch_size: 128
[Aug-03-2025_16-56-30] - neg_num: 99
[Aug-03-2025_16-56-30] - l2_reg: 0
[Aug-03-2025_16-56-30] - l2_emb: 0.0
[Aug-03-2025_16-56-30] - embed_dim: 50
[Aug-03-2025_16-56-30] - hidden_size: 32
[Aug-03-2025_16-56-30] - dropout: 0.2
[Aug-03-2025_16-56-30] - epochs: 1000000
[Aug-03-2025_16-56-30] - early_stop: 50
[Aug-03-2025_16-56-30] - datapath: ../../data/
[Aug-03-2025_16-56-30] - dataset: ml-1m
[Aug-03-2025_16-56-30] - train_data: ml-1m.txt
[Aug-03-2025_16-56-30] - log_path: ../log
[Aug-03-2025_16-56-30] - num_layers: 2
[Aug-03-2025_16-56-30] - num_heads: 1
[Aug-03-2025_16-56-30] - inner_size: 256
[Aug-03-2025_16-56-30] - max_seq_len: 200
[Aug-03-2025_16-56-30] - upload_mode: full
[Aug-03-2025_16-56-30] - skip_test_eval: False
[Aug-03-2025_16-56-30] - eval_freq: 1
[Aug-03-2025_16-56-30] - use_dynamic_sampling: False
[Aug-03-2025_16-56-30] - norm_first: False
[Aug-03-2025_16-56-30] - 训练数据: ../../data/ml-1m/ml-1m.txt
[Aug-03-2025_16-56-30] - 最大序列长度: 200
[Aug-03-2025_16-56-30] - 批次大小: 128
[Aug-03-2025_16-56-33] - 参数上传模式: full
[Aug-03-2025_16-56-33] - 隐私参数（本地更新）: []
[Aug-03-2025_16-56-33] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'trm_encoder.layer.0.filter_layer.complex_weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.weight', 'trm_encoder.layer.0.filter_layer.LayerNorm.bias', 'trm_encoder.layer.0.feed_forward.dense_1.weight', 'trm_encoder.layer.0.feed_forward.dense_1.bias', 'trm_encoder.layer.0.feed_forward.dense_2.weight', 'trm_encoder.layer.0.feed_forward.dense_2.bias', 'trm_encoder.layer.0.feed_forward.LayerNorm.weight', 'trm_encoder.layer.0.feed_forward.LayerNorm.bias', 'trm_encoder.layer.1.filter_layer.complex_weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.weight', 'trm_encoder.layer.1.filter_layer.LayerNorm.bias', 'trm_encoder.layer.1.feed_forward.dense_1.weight', 'trm_encoder.layer.1.feed_forward.dense_1.bias', 'trm_encoder.layer.1.feed_forward.dense_2.weight', 'trm_encoder.layer.1.feed_forward.dense_2.bias', 'trm_encoder.layer.1.feed_forward.LayerNorm.weight', 'trm_encoder.layer.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-03-2025_16-56-33] - 动态负采样: False
[Aug-03-2025_16-56-33] - 用户数量: 6040
[Aug-03-2025_16-56-33] - 物品数量: 3416
[Aug-03-2025_16-58-15] - epoch:1, time: 55.029405(s), valid (NDCG@10: 0.2320, HR@10: 0.4316), test (NDCG@10: 0.2316, HR@10: 0.4315) all_time: 55.029405(s)
[Aug-03-2025_16-58-15] - 新的最佳性能: valid NDCG@10=0.2320, test NDCG@10=0.2316
[Aug-03-2025_16-59-52] - epoch:2, time: 52.017172(s), valid (NDCG@10: 0.2470, HR@10: 0.4452), test (NDCG@10: 0.2424, HR@10: 0.4407) all_time: 107.046577(s)
[Aug-03-2025_16-59-52] - 新的最佳性能: valid NDCG@10=0.2470, test NDCG@10=0.2424
[Aug-03-2025_17-01-31] - epoch:3, time: 51.869583(s), valid (NDCG@10: 0.2570, HR@10: 0.4682), test (NDCG@10: 0.2540, HR@10: 0.4596) all_time: 158.916160(s)
[Aug-03-2025_17-01-31] - 新的最佳性能: valid NDCG@10=0.2570, test NDCG@10=0.2540
[Aug-03-2025_17-03-12] - epoch:4, time: 53.709090(s), valid (NDCG@10: 0.2983, HR@10: 0.5278), test (NDCG@10: 0.2934, HR@10: 0.5129) all_time: 212.625250(s)
[Aug-03-2025_17-03-12] - 新的最佳性能: valid NDCG@10=0.2983, test NDCG@10=0.2934
[Aug-03-2025_17-04-53] - epoch:5, time: 53.740785(s), valid (NDCG@10: 0.3298, HR@10: 0.5632), test (NDCG@10: 0.3249, HR@10: 0.5526) all_time: 266.366035(s)
[Aug-03-2025_17-04-53] - 新的最佳性能: valid NDCG@10=0.3298, test NDCG@10=0.3249
[Aug-03-2025_17-06-34] - epoch:6, time: 54.094727(s), valid (NDCG@10: 0.3612, HR@10: 0.6041), test (NDCG@10: 0.3549, HR@10: 0.5947) all_time: 320.460761(s)
[Aug-03-2025_17-06-34] - 新的最佳性能: valid NDCG@10=0.3612, test NDCG@10=0.3549
[Aug-03-2025_17-08-16] - epoch:7, time: 54.008407(s), valid (NDCG@10: 0.3837, HR@10: 0.6333), test (NDCG@10: 0.3741, HR@10: 0.6123) all_time: 374.469169(s)
[Aug-03-2025_17-08-16] - 新的最佳性能: valid NDCG@10=0.3837, test NDCG@10=0.3741
[Aug-03-2025_17-09-58] - epoch:8, time: 54.740761(s), valid (NDCG@10: 0.4003, HR@10: 0.6522), test (NDCG@10: 0.3907, HR@10: 0.6366) all_time: 429.209930(s)
[Aug-03-2025_17-09-58] - 新的最佳性能: valid NDCG@10=0.4003, test NDCG@10=0.3907
[Aug-03-2025_17-11-39] - epoch:9, time: 54.073620(s), valid (NDCG@10: 0.4124, HR@10: 0.6636), test (NDCG@10: 0.4048, HR@10: 0.6515) all_time: 483.283550(s)
[Aug-03-2025_17-11-39] - 新的最佳性能: valid NDCG@10=0.4124, test NDCG@10=0.4048
[Aug-03-2025_17-13-21] - epoch:10, time: 54.369349(s), valid (NDCG@10: 0.4247, HR@10: 0.6755), test (NDCG@10: 0.4163, HR@10: 0.6611) all_time: 537.652898(s)
[Aug-03-2025_17-13-21] - 新的最佳性能: valid NDCG@10=0.4247, test NDCG@10=0.4163
[Aug-03-2025_17-15-02] - epoch:11, time: 54.425842(s), valid (NDCG@10: 0.4390, HR@10: 0.6866), test (NDCG@10: 0.4262, HR@10: 0.6740) all_time: 592.078741(s)
[Aug-03-2025_17-15-02] - 新的最佳性能: valid NDCG@10=0.4390, test NDCG@10=0.4262
[Aug-03-2025_17-16-45] - epoch:12, time: 54.921418(s), valid (NDCG@10: 0.4421, HR@10: 0.6937), test (NDCG@10: 0.4285, HR@10: 0.6773) all_time: 647.000159(s)
[Aug-03-2025_17-16-45] - 新的最佳性能: valid NDCG@10=0.4421, test NDCG@10=0.4285
[Aug-03-2025_17-18-26] - epoch:13, time: 53.841290(s), valid (NDCG@10: 0.4481, HR@10: 0.6937), test (NDCG@10: 0.4351, HR@10: 0.6853) all_time: 700.841448(s)
[Aug-03-2025_17-18-26] - 新的最佳性能: valid NDCG@10=0.4481, test NDCG@10=0.4351
[Aug-03-2025_17-20-09] - epoch:14, time: 54.473639(s), valid (NDCG@10: 0.4524, HR@10: 0.7041), test (NDCG@10: 0.4387, HR@10: 0.6869) all_time: 755.315087(s)
[Aug-03-2025_17-20-09] - 新的最佳性能: valid NDCG@10=0.4524, test NDCG@10=0.4387
[Aug-03-2025_17-21-51] - epoch:15, time: 53.963784(s), valid (NDCG@10: 0.4586, HR@10: 0.7063), test (NDCG@10: 0.4450, HR@10: 0.6962) all_time: 809.278871(s)
[Aug-03-2025_17-21-51] - 新的最佳性能: valid NDCG@10=0.4586, test NDCG@10=0.4450
[Aug-03-2025_17-23-33] - epoch:16, time: 54.822507(s), valid (NDCG@10: 0.4611, HR@10: 0.7091), test (NDCG@10: 0.4459, HR@10: 0.6932) all_time: 864.101379(s)
[Aug-03-2025_17-23-33] - 新的最佳性能: valid NDCG@10=0.4611, test NDCG@10=0.4459
[Aug-03-2025_17-25-14] - epoch:17, time: 53.486032(s), valid (NDCG@10: 0.4602, HR@10: 0.7079), test (NDCG@10: 0.4471, HR@10: 0.6921) all_time: 917.587410(s)
[Aug-03-2025_17-25-14] - 新的最佳性能: valid NDCG@10=0.4611, test NDCG@10=0.4471
[Aug-03-2025_17-26-56] - epoch:18, time: 54.051740(s), valid (NDCG@10: 0.4644, HR@10: 0.7123), test (NDCG@10: 0.4505, HR@10: 0.6957) all_time: 971.639150(s)
[Aug-03-2025_17-26-56] - 新的最佳性能: valid NDCG@10=0.4644, test NDCG@10=0.4505
[Aug-03-2025_17-28-38] - epoch:19, time: 53.935136(s), valid (NDCG@10: 0.4707, HR@10: 0.7157), test (NDCG@10: 0.4528, HR@10: 0.6944) all_time: 1025.574286(s)
[Aug-03-2025_17-28-38] - 新的最佳性能: valid NDCG@10=0.4707, test NDCG@10=0.4528
[Aug-03-2025_17-30-20] - epoch:20, time: 54.490744(s), valid (NDCG@10: 0.4683, HR@10: 0.7123), test (NDCG@10: 0.4548, HR@10: 0.7012) all_time: 1080.065031(s)
[Aug-03-2025_17-30-20] - 新的最佳性能: valid NDCG@10=0.4707, test NDCG@10=0.4548
[Aug-03-2025_17-32-03] - epoch:21, time: 54.527406(s), valid (NDCG@10: 0.4698, HR@10: 0.7159), test (NDCG@10: 0.4534, HR@10: 0.7028) all_time: 1134.592437(s)
[Aug-03-2025_17-32-03] - 新的最佳性能: valid NDCG@10=0.4707, test NDCG@10=0.4548
[Aug-03-2025_17-33-45] - epoch:22, time: 53.990945(s), valid (NDCG@10: 0.4711, HR@10: 0.7164), test (NDCG@10: 0.4562, HR@10: 0.7043) all_time: 1188.583382(s)
[Aug-03-2025_17-33-45] - 新的最佳性能: valid NDCG@10=0.4711, test NDCG@10=0.4562
[Aug-03-2025_17-35-27] - epoch:23, time: 54.148665(s), valid (NDCG@10: 0.4717, HR@10: 0.7194), test (NDCG@10: 0.4574, HR@10: 0.7031) all_time: 1242.732048(s)
[Aug-03-2025_17-35-27] - 新的最佳性能: valid NDCG@10=0.4717, test NDCG@10=0.4574
[Aug-03-2025_17-37-08] - epoch:24, time: 53.695637(s), valid (NDCG@10: 0.4706, HR@10: 0.7185), test (NDCG@10: 0.4559, HR@10: 0.6995) all_time: 1296.427685(s)
[Aug-03-2025_17-38-49] - epoch:25, time: 54.100054(s), valid (NDCG@10: 0.4733, HR@10: 0.7205), test (NDCG@10: 0.4586, HR@10: 0.7046) all_time: 1350.527739(s)
[Aug-03-2025_17-38-49] - 新的最佳性能: valid NDCG@10=0.4733, test NDCG@10=0.4586
[Aug-03-2025_17-40-31] - epoch:26, time: 54.751025(s), valid (NDCG@10: 0.4722, HR@10: 0.7207), test (NDCG@10: 0.4567, HR@10: 0.7026) all_time: 1405.278764(s)
[Aug-03-2025_17-40-31] - 新的最佳性能: valid NDCG@10=0.4733, test NDCG@10=0.4586
[Aug-03-2025_17-42-14] - epoch:27, time: 55.553329(s), valid (NDCG@10: 0.4709, HR@10: 0.7175), test (NDCG@10: 0.4552, HR@10: 0.7030) all_time: 1460.832092(s)
[Aug-03-2025_17-44-00] - epoch:28, time: 57.214967(s), valid (NDCG@10: 0.4734, HR@10: 0.7136), test (NDCG@10: 0.4574, HR@10: 0.7008) all_time: 1518.047060(s)
[Aug-03-2025_17-45-41] - epoch:29, time: 54.155520(s), valid (NDCG@10: 0.4702, HR@10: 0.7151), test (NDCG@10: 0.4555, HR@10: 0.6998) all_time: 1572.202579(s)
[Aug-03-2025_17-47-23] - epoch:30, time: 53.935276(s), valid (NDCG@10: 0.4670, HR@10: 0.7116), test (NDCG@10: 0.4566, HR@10: 0.7010) all_time: 1626.137856(s)
[Aug-03-2025_17-49-04] - epoch:31, time: 54.273544(s), valid (NDCG@10: 0.4691, HR@10: 0.7146), test (NDCG@10: 0.4563, HR@10: 0.6995) all_time: 1680.411399(s)
[Aug-03-2025_17-50-45] - epoch:32, time: 54.407328(s), valid (NDCG@10: 0.4671, HR@10: 0.7144), test (NDCG@10: 0.4560, HR@10: 0.6988) all_time: 1734.818728(s)
[Aug-03-2025_17-52-27] - epoch:33, time: 53.817786(s), valid (NDCG@10: 0.4724, HR@10: 0.7171), test (NDCG@10: 0.4548, HR@10: 0.7007) all_time: 1788.636514(s)
[Aug-03-2025_17-54-08] - epoch:34, time: 53.658386(s), valid (NDCG@10: 0.4725, HR@10: 0.7164), test (NDCG@10: 0.4578, HR@10: 0.7005) all_time: 1842.294900(s)
[Aug-03-2025_17-55-49] - epoch:35, time: 54.084775(s), valid (NDCG@10: 0.4764, HR@10: 0.7224), test (NDCG@10: 0.4547, HR@10: 0.6985) all_time: 1896.379676(s)
[Aug-03-2025_17-55-49] - 新的最佳性能: valid NDCG@10=0.4764, test NDCG@10=0.4586
[Aug-03-2025_17-57-31] - epoch:36, time: 54.167046(s), valid (NDCG@10: 0.4716, HR@10: 0.7166), test (NDCG@10: 0.4549, HR@10: 0.6949) all_time: 1950.546722(s)
[Aug-03-2025_17-59-12] - epoch:37, time: 54.041907(s), valid (NDCG@10: 0.4705, HR@10: 0.7139), test (NDCG@10: 0.4555, HR@10: 0.6970) all_time: 2004.588629(s)
[Aug-03-2025_18-00-54] - epoch:38, time: 54.549787(s), valid (NDCG@10: 0.4706, HR@10: 0.7175), test (NDCG@10: 0.4520, HR@10: 0.6955) all_time: 2059.138416(s)
[Aug-03-2025_18-02-35] - epoch:39, time: 53.460332(s), valid (NDCG@10: 0.4707, HR@10: 0.7175), test (NDCG@10: 0.4562, HR@10: 0.6990) all_time: 2112.598748(s)
[Aug-03-2025_18-04-16] - epoch:40, time: 53.470224(s), valid (NDCG@10: 0.4701, HR@10: 0.7113), test (NDCG@10: 0.4534, HR@10: 0.6939) all_time: 2166.068972(s)
[Aug-03-2025_18-05-56] - epoch:41, time: 53.814450(s), valid (NDCG@10: 0.4749, HR@10: 0.7180), test (NDCG@10: 0.4542, HR@10: 0.6974) all_time: 2219.883422(s)
[Aug-03-2025_18-07-36] - epoch:42, time: 53.164427(s), valid (NDCG@10: 0.4698, HR@10: 0.7149), test (NDCG@10: 0.4526, HR@10: 0.6957) all_time: 2273.047849(s)
[Aug-03-2025_18-09-16] - epoch:43, time: 53.597016(s), valid (NDCG@10: 0.4706, HR@10: 0.7182), test (NDCG@10: 0.4529, HR@10: 0.6983) all_time: 2326.644865(s)
[Aug-03-2025_18-10-58] - epoch:44, time: 53.975042(s), valid (NDCG@10: 0.4712, HR@10: 0.7167), test (NDCG@10: 0.4577, HR@10: 0.6993) all_time: 2380.619907(s)
[Aug-03-2025_18-12-38] - epoch:45, time: 53.268147(s), valid (NDCG@10: 0.4728, HR@10: 0.7172), test (NDCG@10: 0.4508, HR@10: 0.6921) all_time: 2433.888054(s)
[Aug-03-2025_18-14-18] - epoch:46, time: 53.814337(s), valid (NDCG@10: 0.4728, HR@10: 0.7157), test (NDCG@10: 0.4522, HR@10: 0.6952) all_time: 2487.702390(s)
[Aug-03-2025_18-15-59] - epoch:47, time: 53.553432(s), valid (NDCG@10: 0.4718, HR@10: 0.7126), test (NDCG@10: 0.4564, HR@10: 0.6969) all_time: 2541.255822(s)
[Aug-03-2025_18-17-40] - epoch:48, time: 53.456287(s), valid (NDCG@10: 0.4729, HR@10: 0.7159), test (NDCG@10: 0.4525, HR@10: 0.6949) all_time: 2594.712109(s)
[Aug-03-2025_18-19-22] - epoch:49, time: 53.215794(s), valid (NDCG@10: 0.4715, HR@10: 0.7151), test (NDCG@10: 0.4535, HR@10: 0.6937) all_time: 2647.927903(s)
[Aug-03-2025_18-21-09] - epoch:50, time: 56.325140(s), valid (NDCG@10: 0.4734, HR@10: 0.7146), test (NDCG@10: 0.4530, HR@10: 0.6935) all_time: 2704.253044(s)
[Aug-03-2025_18-22-56] - epoch:51, time: 56.441191(s), valid (NDCG@10: 0.4725, HR@10: 0.7182), test (NDCG@10: 0.4510, HR@10: 0.6977) all_time: 2760.694234(s)
[Aug-03-2025_18-24-43] - epoch:52, time: 56.890027(s), valid (NDCG@10: 0.4745, HR@10: 0.7136), test (NDCG@10: 0.4536, HR@10: 0.6914) all_time: 2817.584262(s)
[Aug-03-2025_18-26-32] - epoch:53, time: 56.993133(s), valid (NDCG@10: 0.4726, HR@10: 0.7192), test (NDCG@10: 0.4549, HR@10: 0.6945) all_time: 2874.577395(s)
[Aug-03-2025_18-28-20] - epoch:54, time: 57.568048(s), valid (NDCG@10: 0.4728, HR@10: 0.7152), test (NDCG@10: 0.4547, HR@10: 0.6962) all_time: 2932.145442(s)
[Aug-03-2025_18-30-09] - epoch:55, time: 57.280417(s), valid (NDCG@10: 0.4742, HR@10: 0.7159), test (NDCG@10: 0.4556, HR@10: 0.6995) all_time: 2989.425860(s)
[Aug-03-2025_18-31-56] - epoch:56, time: 56.734657(s), valid (NDCG@10: 0.4746, HR@10: 0.7171), test (NDCG@10: 0.4578, HR@10: 0.6972) all_time: 3046.160517(s)
[Aug-03-2025_18-33-43] - epoch:57, time: 56.385291(s), valid (NDCG@10: 0.4740, HR@10: 0.7131), test (NDCG@10: 0.4525, HR@10: 0.6930) all_time: 3102.545807(s)
[Aug-03-2025_18-35-31] - epoch:58, time: 56.768269(s), valid (NDCG@10: 0.4707, HR@10: 0.7139), test (NDCG@10: 0.4530, HR@10: 0.6964) all_time: 3159.314076(s)
[Aug-03-2025_18-37-19] - epoch:59, time: 56.744365(s), valid (NDCG@10: 0.4699, HR@10: 0.7137), test (NDCG@10: 0.4534, HR@10: 0.6912) all_time: 3216.058441(s)
[Aug-03-2025_18-39-07] - epoch:60, time: 56.702017(s), valid (NDCG@10: 0.4734, HR@10: 0.7177), test (NDCG@10: 0.4530, HR@10: 0.6957) all_time: 3272.760458(s)
[Aug-03-2025_18-40-55] - epoch:61, time: 56.826745(s), valid (NDCG@10: 0.4733, HR@10: 0.7199), test (NDCG@10: 0.4554, HR@10: 0.6925) all_time: 3329.587203(s)
[Aug-03-2025_18-42-45] - epoch:62, time: 57.221201(s), valid (NDCG@10: 0.4689, HR@10: 0.7142), test (NDCG@10: 0.4521, HR@10: 0.6932) all_time: 3386.808404(s)
[Aug-03-2025_18-44-35] - epoch:63, time: 58.081495(s), valid (NDCG@10: 0.4704, HR@10: 0.7139), test (NDCG@10: 0.4552, HR@10: 0.6975) all_time: 3444.889899(s)
[Aug-03-2025_18-46-26] - epoch:64, time: 59.015688(s), valid (NDCG@10: 0.4726, HR@10: 0.7157), test (NDCG@10: 0.4535, HR@10: 0.6921) all_time: 3503.905586(s)
