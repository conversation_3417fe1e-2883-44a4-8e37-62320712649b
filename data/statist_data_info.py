import os
from collections import defaultdict

def analyze_dataset(file_path):
    """
    Analyzes a dataset file to calculate statistics.

    Args:
        file_path (str): The path to the dataset file.

    Returns:
        dict: A dictionary containing the statistics.
    """
    users = set()
    items = set()
    interaction_count = 0

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) < 2:
                    continue
                
                user_id, item_id = parts[0], parts[1]
                users.add(user_id)
                items.add(item_id)
                interaction_count += 1
    except FileNotFoundError:
        return None

    num_users = len(users)
    num_items = len(items)
    
    if num_users == 0:
        avg_seq_len = 0
    else:
        avg_seq_len = interaction_count / num_users

    return {
        "Users": num_users,
        "Items": num_items,
        "Interactions": interaction_count,
        "Avg. Seq. Length": f"{avg_seq_len:.2f}"
    }

def main():
    # The script is in the 'data' directory, so we can use relative paths.
    data_dir = os.path.dirname(os.path.abspath(__file__))
    
    datasets = {
        "ML-100K": os.path.join ("ml-100k", "ml-100k.txt"),
        "ML-1M": os.path.join ("ml-1m", "ml-1m.txt"),
        "LastFM": os.path.join ("LastFM", "LastFM.txt"),
        "Toys": os.path.join ("Toys_and_Games", "Toys_and_Games.txt"),
        "Video": os.path.join ("Video", "Video.txt"),
        "Yelp": os.path.join ("Yelp", "Yelp.txt"),
        "Sports": os.path.join ("Sports_and_Outdoors", "Sports_and_Outdoors.txt"),
        "Beauty": os.path.join("beauty", "beauty.txt"),
        "Steam": os.path.join("Steam", "Steam.txt"),
    }

    print(f"{'Dataset':<25} | {'Users(Seq)':>12} | {'Items':>10} | {'Interactions':>15} | {'Avg. Seq. Length':>20}")
    print("-" * 95)

    for name, rel_path in datasets.items():
        file_path = os.path.join(data_dir, rel_path)
        stats = analyze_dataset(file_path)
        if stats:
            print(f"{name:<25} | {stats['Users']:>12} | {stats['Items']:>10} | {stats['Interactions']:>15} | {stats['Avg. Seq. Length']:>20}")
        else:
            print(f"Could not find or process {name} dataset at {file_path}")

if __name__ == "__main__":
    main()
